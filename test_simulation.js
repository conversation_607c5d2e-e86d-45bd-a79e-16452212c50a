// test_simulation.js

// --- Simulation State and Core Logic (Copied for testing purposes) ---
// In a real-world scenario, these would be exported from script.js as modules.

const simulationState = {
    isRunning: false,
    tickCounter: 0,
    intervalId: null, // Not used in this test file, as it's not browser-based
    entities: [],
    gridSize: 10,
    cellSize: 50, // Not directly used in non-visual tests
    resourceSpawnChance: 0.1,
    resourceMaxAmount: 5,
    resourceRegenerationRate: 5,
    passiveDangerThreshold: 5,
    childEnergyThreshold: 25, // Balanced child energy threshold
    activeChildGrowthRate: 0.08,
    passiveChildGrowthRate: 0.12, // Balanced passive child growth rate
    childProximityForCreation: 2,
    powerGainOnAttack: 5,
    powerCostOfChildCreation: 8, // Balanced power cost for child creation
    passiveCooperationProximity: 2,
    passiveCooperationAttackProximity: 1.5,
    passiveCooperationDamage: 3,
    reproductionCooldownTicks: 100,
    childEnergyConsumptionRate: 0.3,
};

const initialEntities = [
    { id: 'active1', type: 'active', label: 'Active 1', icon: 'A', position: { row: 0, col: 0 }, energy: 170, trait: 'High Energy', speed: 1, isDangerous: false, dangerLevel: 0, targetPosition: null, childGenerationThreshold: 10, power: 20, canReproduce: true, lastReproducedTick: 0 },
    { id: 'active2', type: 'active', label: 'Active 2', icon: 'A', position: { row: 0, col: 1 }, energy: 170, trait: 'Fast', speed: 2, isDangerous: false, dangerLevel: 0, targetPosition: null, childGenerationThreshold: 10, power: 18, canReproduce: true, lastReproducedTick: 0 },
    { id: 'active3', type: 'active', label: 'Active 3', icon: 'A', position: { row: 1, col: 0 }, energy: 170, trait: 'Aggressive', speed: 1.5, isDangerous: false, dangerLevel: 0, targetPosition: null, childGenerationThreshold: 10, power: 19, canReproduce: true, lastReproducedTick: 0 },
    { id: 'active4', type: 'active', label: 'Active 4', icon: 'A', position: { row: 1, col: 1 }, energy: 170, trait: 'Adaptive', speed: 1.2, isDangerous: false, dangerLevel: 0, targetPosition: null, childGenerationThreshold: 10, power: 17, canReproduce: true, lastReproducedTick: 0 },
    { id: 'passive1', type: 'passive', label: 'Passive 1', icon: 'P', position: { row: 9, col: 9 }, energy: 250, trait: 'Shielded', speed: 0.5, isDangerous: false, dangerLevel: 0, targetPosition: null, canAttack: false, canReproduce: true, power: 5, lastReproducedTick: 0 },
    { id: 'passive2', type: 'passive', label: 'Passive 2', icon: 'P', position: { row: 9, col: 8 }, energy: 250, trait: 'Resilient', speed: 0.5, isDangerous: false, dangerLevel: 0, targetPosition: null, canAttack: false, canReproduce: true, power: 4, lastReproducedTick: 0 },
    { id: 'resource1', type: 'resource', label: 'Resource 1', icon: '🌱', position: { row: 5, col: 5 }, currentAmount: 100, energyPerTurn: 1, growth: 0, isHidden: false },
    { id: 'resource2', type: 'resource', label: 'Resource 2', icon: '🌱', position: { row: 4, col: 4 }, currentAmount: 100, energyPerTurn: 1, growth: 0, isHidden: false },
];

// Helper to calculate distance (needed for interactions)
function getDistance(pos1, pos2) {
    return Math.sqrt(
        Math.pow(pos1.row - pos2.row, 2) +
        Math.pow(pos1.col - pos2.col, 2)
    );
}

// Function to check if a target position is occupied (comprehensive for test)
function isTargetOccupied(row, col, currentAgentId) {
    return simulationState.entities.some(e => 
        e.id !== currentAgentId && 
        e.position.row === row && e.position.col === col
    );
}

// Function to add messages to the UI log panel (simplified for test)
function logToUI(message) {
    // In test environment, simply log to console without tick prefix
    console.log(message);
}

// Reimplement core simulation functions
function updateEntityStates() {
    simulationState.entities.filter(e => e.type === 'active' || e.type === 'passive' || e.type === 'child').forEach(entity => {
        if (entity.type === 'active') {
            entity.energy = Math.max(0, entity.energy - 0.9); // Balanced energy drain for active agents
        } else if (entity.type === 'passive') {
            entity.energy = Math.max(0, entity.energy - 0.8); // Slightly higher energy drain for passive agents
        } else if (entity.type === 'child') {
            entity.energy = Math.max(0, entity.energy - 0.3); // Reduced child energy consumption for survival
        }

        if (entity.type === 'passive') {
            let nearbyActiveAgents = simulationState.entities.filter(other =>
                other.type === 'active' &&
                getDistance(entity.position, other.position) < 3
            );
            entity.dangerLevel = nearbyActiveAgents.length;
            entity.isDangerous = entity.dangerLevel > simulationState.passiveDangerThreshold;
        }

        if (entity.type === 'child') {
            entity.growthProgress = Math.min(100, entity.growthProgress + (entity.assignedGrowthRate || simulationState.activeChildGrowthRate));

            if (entity.growthProgress < 100 && entity.inheritedPower !== undefined && entity.inheritedSpeed !== undefined) {
                const growthFactor = (entity.assignedGrowthRate || simulationState.activeChildGrowthRate) / 100;
                entity.power = Math.min(entity.inheritedPower, entity.power + (entity.inheritedPower - entity.power) * growthFactor);
                entity.speed = Math.min(entity.inheritedSpeed, entity.speed + (entity.inheritedSpeed - entity.speed) * growthFactor);
            }

            if (entity.growthProgress >= 100 && !entity.isMature) {
                entity.isMature = true;

                // Determine mature type based on parent types
                let matureType;
                let matureIcon;

                if (entity.parentTypes && entity.parentTypes.every(type => type === 'active')) {
                    matureType = 'active';
                    matureIcon = 'A'; // Match legend for mature active
                } else if (entity.parentTypes && entity.parentTypes.every(type => type === 'passive')) {
                    matureType = 'passive';
                    matureIcon = 'P'; // Match legend for mature passive
                } else if (entity.parentTypes && entity.parentTypes.some(type => type === 'active') && entity.parentTypes.some(type => type === 'passive')) {
                    // Mixed parentage (e.g., one active, one passive) -> child becomes active
                    matureType = 'active';
                    matureIcon = 'A'; // Match legend for mature active
                } else {
                    // Fallback, though ideally parentTypes should always be defined
                    matureType = 'passive'; // Default to passive if parent types are unclear
                    matureIcon = 'P'; // Match legend for mature passive
                }

                entity.type = matureType; // Set mature type
                entity.label = `Mature ${entity.label.replace('Child ', '')}`; // Update label
                entity.icon = matureIcon; // Set icon based on mature type
                entity.energy = 50; // Starting energy for new agent

                if (entity.inheritedSpeed !== undefined) entity.speed = entity.inheritedSpeed;
                if (entity.inheritedPower !== undefined) entity.power = entity.inheritedPower;

                entity.isDangerous = false;
                entity.dangerLevel = 0;
                entity.targetPosition = null;
                entity.canAttack = (matureType === 'active');
                entity.canReproduce = (matureType === 'active');

                if (!entity.trait) {
                    const traits = ['Shielded', 'Resilient', 'Observant', 'Calm', 'Fast', 'Strong', 'Adaptive', 'Aggressive', 'Stealthy', 'High Energy'];
                    entity.trait = traits[Math.floor(Math.random() * traits.length)];
                }
            }
        }
    });

    const deadAgents = simulationState.entities.filter(e => (e.type === 'active' || e.type === 'passive') && e.energy <= 0);
    deadAgents.forEach(deadAgent => {
    });
    simulationState.entities = simulationState.entities.filter(e => !(e.type === 'active' || e.type === 'passive') || e.energy > 0);
}

function handleMovement() {
    simulationState.entities.filter(e => e.type === 'active' || e.type === 'passive').forEach(agent => {
        let intendedRow = agent.position.row;
        let intendedCol = agent.position.col;
        let moved = false;

        let targetEntity = null;
        let targetDirection = null;

        if (agent.type === 'passive' && agent.isDangerous) {
            let closestActive = null;
            let minDistance = Infinity;
            simulationState.entities.filter(e => e.type === 'active').forEach(activeAgent => {
                const dist = getDistance(agent.position, activeAgent.position);
                if (dist < minDistance) {
                    minDistance = dist;
                    closestActive = activeAgent;
                }
            });

            if (closestActive) {
                targetEntity = closestActive;
                const dy = agent.position.row - targetEntity.position.row;
                const dx = agent.position.col - targetEntity.position.col;

                if (Math.abs(dy) > Math.abs(dx)) {
                    targetDirection = { dr: Math.sign(dy), dc: 0 };
                } else if (Math.abs(dx) > Math.abs(dy)) {
                    targetDirection = { dr: 0, dc: Math.sign(dx) };
                } else if (dy !== 0 || dx !== 0) {
                    if (Math.random() < 0.5) {
                        targetDirection = { dr: Math.sign(dy), dc: 0 };
                    } else {
                        targetDirection = { dr: 0, dc: Math.sign(dx) };
                    }
                } else {
                    targetDirection = { dr: 0, dc: 0 };
                }
            }
        }

        if (!targetDirection && agent.energy < 70 && !agent.isDangerous) {
            let closestResource = null;
            let minDistance = Infinity;
            simulationState.entities.filter(e => e.type === 'resource' && e.currentAmount > 0 && !e.isHidden).forEach(resource => {
                const dist = getDistance(agent.position, resource.position);
                if (dist < minDistance) {
                    minDistance = dist;
                    closestResource = resource;
                }
            });

            if (closestResource) {
                targetEntity = closestResource;
                const dy = targetEntity.position.row - agent.position.row;
                const dx = targetEntity.position.col - agent.position.col;

                if (Math.abs(dy) > Math.abs(dx)) {
                    targetDirection = { dr: Math.sign(dy), dc: 0 };
                } else if (Math.abs(dx) > Math.abs(dy)) {
                    targetDirection = { dr: 0, dc: Math.sign(dx) };
                } else if (dy !== 0 || dx !== 0) {
                    if (Math.random() < 0.5) {
                        targetDirection = { dr: Math.sign(dy), dc: 0 };
                    } else {
                        targetDirection = { dr: 0, dc: Math.sign(dx) };
                    }
                } else {
                    targetDirection = { dr: 0, dc: 0 };
                }
            }
        }

        if (!targetDirection) {
            let closestAgent = null;
            let minDistance = Infinity;
            simulationState.entities.filter(e => (e.type === 'active' || e.type === 'passive') && e.id !== agent.id).forEach(otherAgent => {
                const dist = getDistance(agent.position, otherAgent.position);
                if (dist < minDistance) {
                    minDistance = dist;
                    closestAgent = otherAgent;
                }
            });

            if (closestAgent) {
                targetEntity = closestAgent;
                const dy = targetEntity.position.row - agent.position.row;
                const dx = targetEntity.position.col - agent.position.col;

                if (Math.abs(dy) > Math.abs(dx)) {
                    targetDirection = { dr: Math.sign(dy), dc: 0 };
                } else if (Math.abs(dx) > Math.abs(dy)) {
                    targetDirection = { dr: 0, dc: Math.sign(dx) };
                } else if (dy !== 0 || dx !== 0) {
                    if (Math.random() < 0.5) {
                        targetDirection = { dr: Math.sign(dy), dc: 0 };
                    } else {
                        targetDirection = { dr: 0, dc: Math.sign(dx) };
                    }
                }
            } else {
                const cardinalDirections = [
                    {dr: -1, dc: 0}, // Up
                    {dr: 1, dc: 0},  // Down
                    {dr: 0, dc: -1}, // Left
                    {dr: 0, dc: 1},  // Right
                    {dr: 0, dc: 0}   // Stay put
                ];
                targetDirection = cardinalDirections[Math.floor(Math.random() * cardinalDirections.length)];
            }
        }

        if (targetDirection) {
            intendedRow = agent.position.row + targetDirection.dr;
            intendedCol = agent.position.col + targetDirection.dc;
        }

        intendedRow = Math.max(0, Math.min(simulationState.gridSize - 1, intendedRow));
        intendedCol = Math.max(0, Math.min(simulationState.gridSize - 1, intendedCol));

        if (!isTargetOccupied(intendedRow, intendedCol, agent.id)) {
            agent.position.row = intendedRow;
            agent.position.col = intendedCol;
            moved = true;
        } else {
            const fallbackDirections = [
                {dr: 0, dc: 1}, {dr: 0, dc: -1}, {dr: 1, dc: 0}, {dr: -1, dc: 0} // Cardinal (no stay put here as agent already tried to move)
            ];
            fallbackDirections.sort(() => Math.random() - 0.5);

            for (const dir of fallbackDirections) {
                let tempRow = agent.position.row + dir.dr;
                let tempCol = agent.position.col + dir.dc;

                tempRow = Math.max(0, Math.min(simulationState.gridSize - 1, tempRow));
                tempCol = Math.max(0, Math.min(simulationState.gridSize - 1, tempCol));

                if (!isTargetOccupied(tempRow, tempCol, agent.id)) {
                    agent.position.row = tempRow;
                    agent.position.col = tempCol;
                    moved = true;
                    break;
                }
            }
        }
    });
}

function handleInteractions() {
    const availableResources = simulationState.entities.filter(e => e.type === 'resource' && !e.isHidden).length;
    const resourceScarcityThreshold = simulationState.resourceMaxAmount / 2;
    const isResourceScarce = availableResources < resourceScarcityThreshold;

    simulationState.entities.forEach(entity1 => {
        simulationState.entities.forEach(entity2 => {
            if (entity1.id === entity2.id) return;

            const distance = getDistance(entity1.position, entity2.position);

            if ((entity1.type === 'active' || entity1.type === 'passive') && entity2.type === 'resource' && distance < 2 && entity2.currentAmount > 0 && !entity2.isHidden) {
                let gatherRate = 2.5; // Base gather rate
                if (entity1.trait === 'Fast') gatherRate = 3;
                if (entity1.type === 'passive') gatherRate = 1.0; // Passive agents have adjusted gather rate
                if (entity1.type === 'passive' && entity1.isDangerous) gatherRate *= 0.5; 
                
                const gatheredAmount = Math.min(gatherRate, entity2.currentAmount);
                entity1.energy += gatheredAmount;
                entity2.currentAmount -= gatheredAmount;
                // Passive agents gain power from gathering resources
                if (entity1.type === 'passive') {
                    entity1.power = (entity1.power || 0) + 0.3; // Adjusted power gain for gathering
                }
            }

            if (entity1.type === 'active' && (entity2.type === 'active' || entity2.type === 'passive' || entity2.type === 'child') && distance < 1.5) {
                const attackChance = isResourceScarce ? 0.4 : 0.1;
                if (Math.random() < attackChance) {
                    let damage = 15;
                    let attackSuccessful = true;

                    if (entity2.type === 'passive') {
                        if (Math.random() < 0.2) {
                            damage *= 0.2;
                            entity1.energy = Math.max(0, entity1.energy - 1);
                            attackSuccessful = false;
                        }
                    }

                    entity2.energy = Math.max(0, entity2.energy - damage);
                    entity1.energy = Math.max(0, entity1.energy - 2);

                    if (entity2.type === 'active') {
                        const powerGained = simulationState.powerGainOnAttack;
                        entity1.power = (entity1.power || 0) + powerGained;
                    }
                }
            }

            if (distance < 1.5) {
                if (entity1.type === 'active' && entity2.type === 'passive' && entity1.energy > 50 && entity2.energy < 50) {
                    if (Math.random() < 0.1) {
                        entity1.energy -= 5;
                        entity2.energy += 5;
                    }
                }
                else if (entity1.type === 'passive' && entity2.type === 'passive' && entity1.energy > 10 && entity2.energy > 10) {
                    if (Math.random() < 0.1) {
                        entity1.energy += 5;
                        entity2.energy += 5;
                        // Passive agents gain power from cooperation
                        entity1.power = (entity1.power || 0) + 0.12; // Adjusted power gain for cooperation
                        entity2.power = (entity2.power || 0) + 0.12; // Both gain power
                    }
                }
            }

            if (entity1.type === 'active') {
                const nearbyPassives = simulationState.entities.filter(e =>
                    e.type === 'passive' &&
                    e.id !== entity1.id &&
                    getDistance(entity1.position, e.position) < simulationState.passiveCooperationAttackProximity
                );

                if (nearbyPassives.length >= 2) {
                    let isCooperatingGroup = false;
                    for (let i = 0; i < nearbyPassives.length; i++) {
                        for (let j = i + 1; j < nearbyPassives.length; j++) {
                            const passive1 = nearbyPassives[i];
                            const passive2 = nearbyPassives[j];
                            const passiveDistance = getDistance(passive1.position, passive2.position);
                            if (passiveDistance < simulationState.passiveCooperationProximity) {
                                isCooperatingGroup = true;
                                break;
                            }
                        }
                        if (isCooperatingGroup) break;
                    }

                    if (isCooperatingGroup) {
                        if (Math.random() < 0.1) {
                            entity1.energy = Math.max(0, entity1.energy - simulationState.passiveCooperationDamage);
                        }
                    }
                }
            }

            if (entity1.type === 'passive' && entity2.type === 'child' && distance < 1.5) {
                if (Math.random() < 0.1) {
                    const growthBoost = 5;
                    entity2.growthProgress = Math.min(100, entity2.growthProgress + growthBoost);
                }
            }
        });
    });

    simulationState.entities.filter(e => e.type === 'active' && e.energy >= simulationState.childEnergyThreshold && (e.power || 0) >= 20).forEach(activeAgent1 => {
        // Check cooldown for activeAgent1
        if (simulationState.tickCounter - (activeAgent1.lastReproducedTick || 0) < simulationState.reproductionCooldownTicks) {
            return; // Agent is on cooldown
        }
        const activeAgent2 = simulationState.entities.find(e =>
            e.id !== activeAgent1.id &&
            e.type === 'active' &&
            e.energy >= simulationState.childEnergyThreshold &&
            (e.power || 0) >= 20 &&
            // Check cooldown for activeAgent2
            (simulationState.tickCounter - (e.lastReproducedTick || 0) >= simulationState.reproductionCooldownTicks) &&
            getDistance(activeAgent1.position, e.position) < simulationState.childProximityForCreation
        );

        if (!activeAgent2 || !activeAgent1.canReproduce) return;

        let newChildPosition = null;
        const searchRadius = 2;
        for (let r = -searchRadius; r <= searchRadius; r++) {
            for (let c = -searchRadius; c <= searchRadius; c++) {
                let potentialRow = activeAgent1.position.row + r;
                let potentialCol = activeAgent1.position.col + c;

                if (potentialRow >= 0 && potentialCol < simulationState.gridSize &&
                    potentialCol >= 0 && potentialCol < simulationState.gridSize) {
                    const occupied = isTargetOccupied(potentialRow, potentialCol, null);
                    if (!occupied) {
                        newChildPosition = { row: potentialRow, col: potentialCol };
                        break;
                    }
                }
            }
            if (newChildPosition) break;
        }

        if (newChildPosition) {
            const newChildId = `child${simulationState.tickCounter}-${Math.floor(Math.random() * 100)}`;
            const newChild = {
                id: newChildId,
                type: 'child',
                label: `Child ${activeAgent1.label.replace('Active ', '')}`,
                icon: '🧬',
                position: newChildPosition,
                energy: 20,
                trait: activeAgent1.trait,
                isMature: false,
                growthProgress: 0,
                parentTrait: activeAgent1.trait,
                power: Math.max(1, Math.floor(activeAgent1.power * 0.5)),
                speed: activeAgent1.speed > 0.5 ? (activeAgent1.speed * 0.5) : 0.5,
                inheritedPower: activeAgent1.power,
                inheritedSpeed: activeAgent1.speed,
                parentIds: [activeAgent1.id, activeAgent2.id],
                parentTypes: [activeAgent1.type, activeAgent2.type],
                assignedGrowthRate: simulationState.activeChildGrowthRate,
            };
            simulationState.entities.push(newChild);
            activeAgent1.energy -= simulationState.childEnergyThreshold;
            activeAgent1.power = Math.max(0, activeAgent1.power - simulationState.powerCostOfChildCreation);
            activeAgent1.lastReproducedTick = simulationState.tickCounter;
            activeAgent2.energy -= simulationState.childEnergyThreshold;
            activeAgent2.power = Math.max(0, activeAgent2.power - simulationState.powerCostOfChildCreation);
            activeAgent2.lastReproducedTick = simulationState.tickCounter;
        }
    });

    simulationState.entities.filter(e => e.type === 'passive' && e.energy >= simulationState.childEnergyThreshold && (e.power || 0) >= 20).forEach(passiveAgent1 => {
        // Check cooldown for passiveAgent1
        if (simulationState.tickCounter - (passiveAgent1.lastReproducedTick || 0) < simulationState.reproductionCooldownTicks) {
            return; // Agent is on cooldown
        }
        const passiveAgent2 = simulationState.entities.find(e =>
            e.id !== passiveAgent1.id &&
            e.type === 'passive' &&
            e.energy >= simulationState.childEnergyThreshold &&
            // Check cooldown for passiveAgent2
            (simulationState.tickCounter - (e.lastReproducedTick || 0) >= simulationState.reproductionCooldownTicks) &&
            getDistance(passiveAgent1.position, e.position) < simulationState.childProximityForCreation
        );

        if (!passiveAgent2 || !passiveAgent1.canReproduce) return;

        let newChildPosition = null;
        const searchRadius = 2;
        for (let r = -searchRadius; r <= searchRadius; r++) {
            for (let c = -searchRadius; c <= searchRadius; c++) {
                let potentialRow = passiveAgent1.position.row + r;
                let potentialCol = passiveAgent1.position.col + c;

                if (potentialRow >= 0 && potentialCol < simulationState.gridSize &&
                    potentialCol >= 0 && potentialCol < simulationState.gridSize) {
                    const occupied = isTargetOccupied(potentialRow, potentialCol, null);
                    if (!occupied) {
                        newChildPosition = { row: potentialRow, col: potentialCol };
                        break;
                    }
                }
            }
            if (newChildPosition) break;
        }

        if (newChildPosition) {
            const newChildId = `child${simulationState.tickCounter}-${Math.floor(Math.random() * 100)}`;
            const newChild = {
                id: newChildId,
                type: 'child',
                label: `Child ${passiveAgent1.label.replace('Passive ', '')}`,
                icon: '🧬',
                position: newChildPosition,
                energy: 20,
                trait: passiveAgent1.trait,
                isMature: false,
                growthProgress: 0,
                parentTrait: passiveAgent1.trait,
                power: Math.max(1, Math.floor(passiveAgent1.power * 0.5)),
                speed: passiveAgent1.speed > 0.5 ? (passiveAgent1.speed * 0.5) : 0.5,
                inheritedPower: passiveAgent1.power,
                inheritedSpeed: passiveAgent1.speed,
                parentIds: [passiveAgent1.id, passiveAgent2.id],
                parentTypes: [passiveAgent1.type, passiveAgent2.type],
                assignedGrowthRate: simulationState.passiveChildGrowthRate,
            };
            simulationState.entities.push(newChild);
            passiveAgent1.energy -= simulationState.childEnergyThreshold;
            passiveAgent1.power = Math.max(0, passiveAgent1.power - simulationState.powerCostOfChildCreation);
            passiveAgent1.lastReproducedTick = simulationState.tickCounter;
            passiveAgent2.energy -= simulationState.childEnergyThreshold;
            passiveAgent2.power = Math.max(0, passiveAgent2.power - simulationState.powerCostOfChildCreation);
            passiveAgent2.lastReproducedTick = simulationState.tickCounter;
        }
    });
}

function updateResources() {
    simulationState.entities.filter(e => e.type === 'resource').forEach(resource => {
        if (resource.currentAmount <= 0) {
            resource.isHidden = true;
            resource.currentAmount = Math.min(100, resource.currentAmount + simulationState.resourceRegenerationRate);
            if (resource.currentAmount >= 50) {
                let newRow, newCol;
                let positionFound = false;
                for (let i = 0; i < 100; i++) {
                    newRow = Math.floor(Math.random() * simulationState.gridSize);
                    newCol = Math.floor(Math.random() * simulationState.gridSize);
                    const occupied = isTargetOccupied(newRow, newCol, null);
                    if (!occupied) {
                        positionFound = true;
                        break;
                    }
                }

                if (positionFound) {
                    resource.position.row = newRow;
                    resource.position.col = newCol;
                    resource.isHidden = false;
                }
            }
        } else if (resource.isHidden) {
            resource.currentAmount = Math.min(100, resource.currentAmount + simulationState.resourceRegenerationRate);
            if (resource.currentAmount >= 50) {
                let newRow, newCol;
                let positionFound = false;
                for (let i = 0; i < 100; i++) {
                    newRow = Math.floor(Math.random() * simulationState.gridSize);
                    newCol = Math.floor(Math.random() * simulationState.gridSize);
                    const occupied = isTargetOccupied(newRow, newCol, null);
                    if (!occupied) {
                        positionFound = true;
                        break;
                    }
                }

                if (positionFound) {
                    resource.position.row = newRow;
                    resource.position.col = newCol;
                    resource.isHidden = false;
                }
            }
        } else {
            resource.currentAmount = Math.min(100, resource.currentAmount + resource.energyPerTurn);
        }
    });

    const visibleResources = simulationState.entities.filter(e => e.type === 'resource' && !e.isHidden).length;
    const hiddenResources = simulationState.entities.filter(e => e.type === 'resource' && e.isHidden).length;

    if (visibleResources < simulationState.resourceMaxAmount && hiddenResources === 0 && Math.random() < simulationState.resourceSpawnChance) {
        let newRow, newCol;
        let positionFound = false;
        for (let i = 0; i < 100; i++) {
            newRow = Math.floor(Math.random() * simulationState.gridSize);
            newCol = Math.floor(Math.random() * simulationState.gridSize);
            const occupied = isTargetOccupied(newRow, newCol, null);
            if (!occupied) {
                positionFound = true;
                break;
            }
        }

        if (positionFound) {
            const newResourceId = `resource${simulationState.tickCounter}-${Math.floor(Math.random() * 100)}`;
            const newResource = {
                id: newResourceId,
                type: 'resource',
                label: 'New Resource',
                icon: '🌱',
                position: { row: newRow, col: newCol },
                currentAmount: 100,
                energyPerTurn: 1,
                growth: 0,
                isHidden: false,
            };
            simulationState.entities.push(newResource);
        }
    }
}

function runSimulationForTicks(ticks) {
    simulationState.tickCounter = 0;
    simulationState.entities = JSON.parse(JSON.stringify(initialEntities));

    for (let i = 0; i < ticks; i++) {
        simulationState.tickCounter++;
        updateEntityStates();
        handleMovement();
        handleInteractions();
        updateResources();
    }
}

function analyzeResults() {
    const activeAgents = simulationState.entities.filter(e => e.type === 'active');
    const passiveAgents = simulationState.entities.filter(e => e.type === 'passive');
    const children = simulationState.entities.filter(e => e.type === 'child');
    const resources = simulationState.entities.filter(e => e.type === 'resource');

    // Calculate initial counts (assuming initialEntities is the starting point)
    const initialActiveCount = initialEntities.filter(e => e.type === 'active').length;
    const initialPassiveCount = initialEntities.filter(e => e.type === 'passive').length;
    const initialResourceCount = initialEntities.filter(e => e.type === 'resource').length;

    // Matured children are now 'active' or 'passive' but retain 'child' in their ID (if implemented this way)
    const maturedActiveChildren = activeAgents.filter(a => a.id.startsWith('child'));
    const maturedPassiveChildren = passiveAgents.filter(p => p.id.startsWith('child'));

    console.log(`--- Simulation Results after ${simulationState.tickCounter} ticks ---`);
    console.log(`
    **Population Overview:**
    Initial Active Agents: ${initialActiveCount}
    Initial Passive Agents: ${initialPassiveCount}
    Final Active Agents: ${activeAgents.length} (Matured from Child: ${maturedActiveChildren.length})
    Final Passive Agents: ${passiveAgents.length} (Matured from Child: ${maturedPassiveChildren.length})
    Current Immature Children: ${children.length}
    Total Entities on Grid: ${simulationState.entities.length}
    `);

    if (activeAgents.length > 0) {
        const avgActiveEnergy = activeAgents.reduce((sum, a) => sum + a.energy, 0) / activeAgents.length;
        const minActiveEnergy = Math.min(...activeAgents.map(a => a.energy));
        const maxActiveEnergy = Math.max(...activeAgents.map(a => a.energy));
        const avgActivePower = activeAgents.reduce((sum, a) => sum + a.power, 0) / activeAgents.length;
        const minActivePower = Math.min(...activeAgents.map(a => a.power));
        const maxActivePower = Math.max(...activeAgents.map(a => a.power));
        console.log(`
    **Active Agent Statistics:**
    Average Energy: ${avgActiveEnergy.toFixed(2)} (Min: ${minActiveEnergy.toFixed(2)}, Max: ${maxActiveEnergy.toFixed(2)})
    Average Power: ${avgActivePower.toFixed(2)} (Min: ${minActivePower.toFixed(2)}, Max: ${maxActivePower.toFixed(2)})
    `);
    } else {
        console.log('No Active Agents remaining.');
    }

    if (passiveAgents.length > 0) {
        const avgPassiveEnergy = passiveAgents.reduce((sum, p) => sum + p.energy, 0) / passiveAgents.length;
        const minPassiveEnergy = Math.min(...passiveAgents.map(p => p.energy));
        const maxPassiveEnergy = Math.max(...passiveAgents.map(p => p.energy));
        const avgPassivePower = passiveAgents.reduce((sum, p) => sum + p.power, 0) / passiveAgents.length;
        const minPassivePower = Math.min(...passiveAgents.map(p => p.power));
        const maxPassivePower = Math.max(...passiveAgents.map(p => p.power));
        console.log(`
    **Passive Agent Statistics:**
    Average Energy: ${avgPassiveEnergy.toFixed(2)} (Min: ${minPassiveEnergy.toFixed(2)}, Max: ${maxPassiveEnergy.toFixed(2)})
    Average Power: ${avgPassivePower.toFixed(2)} (Min: ${minPassivePower.toFixed(2)}, Max: ${maxPassivePower.toFixed(2)})
    `);
    } else {
        console.log('No Passive Agents remaining.');
    }

    if (children.length > 0) {
        const avgChildGrowth = children.reduce((sum, c) => sum + c.growthProgress, 0) / children.length;
        const minChildGrowth = Math.min(...children.map(c => c.growthProgress));
        const maxChildGrowth = Math.max(...children.map(c => c.growthProgress));
        console.log(`
    **Child Statistics:**
    Average Growth Progress: ${avgChildGrowth.toFixed(2)}% (Min: ${minChildGrowth.toFixed(2)}%, Max: ${maxChildGrowth.toFixed(2)}%)
    `);
    } else {
        console.log('No Immature Children remaining.');
    }

    const visibleResources = resources.filter(r => !r.isHidden).length;
    const hiddenResources = resources.filter(r => r.isHidden).length;
    const totalResourceAmount = resources.reduce((sum, r) => sum + r.currentAmount, 0);

    console.log(`
    **Resource Statistics:**
    Initial Resources: ${initialResourceCount}
    Visible Resources: ${visibleResources}
    Hidden (Regenerating) Resources: ${hiddenResources}
    Total Resource Units on Grid: ${totalResourceAmount.toFixed(2)}
    `);

    console.log('--------------------------------------');
}

// --- Test Scenarios ---

function runFairnessTest(testName, ticks = 500) {
    console.log(`\n--- Running Test Scenario: ${testName} ---`);
    runSimulationForTicks(ticks);
    analyzeResults();
}

// Example Test Runs
runFairnessTest("Balanced Initial State", 2000);

// You can uncomment and modify these or add more scenarios
// simulationState.powerGainOnAttack = 0; // Example: Disable power gain for active agents
// runFairnessTest("No Active Power Gain", 500);

// simulationState.passiveCooperationDamage = 0; // Example: Disable passive cooperative damage
// runFairnessTest("No Passive Cooperation Damage", 500); 