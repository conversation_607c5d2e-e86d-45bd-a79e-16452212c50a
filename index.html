<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Artificial Life Simulation Diagram</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="left-section">
        <!-- Current Tick Counter -->
        <div class="tick-counter-container">
            <span>Current Tick:</span> <span id="tick-counter" style="font-weight: bold;">0</span>
        </div>
        <div class="simulation-container"></div>

        <!-- Global SVG Definitions for Arrowheads -->
        <svg style="position: absolute; width: 0; height: 0; overflow: hidden;">
            <defs>
                <marker id="arrowhead-attack" viewBox="0 0 10 10" refX="9" refY="5" markerWidth="8" markerHeight="8" orient="auto-start-reverse">
                    <path d="M 0 0 L 10 5 L 0 10 z" class="arrow-head"></path>
                </marker>
                <marker id="arrowhead-gather" viewBox="0 0 10 10" refX="9" refY="5" markerWidth="8" markerHeight="8" orient="auto-start-reverse">
                    <path d="M 0 0 L 10 5 L 0 10 z" class="arrow-head"></path>
                </marker>
                <marker id="arrowhead-inherit" viewBox="0 0 10 10" refX="9" refY="5" markerWidth="8" markerHeight="8" orient="auto-start-reverse">
                    <path d="M 0 0 L 10 5 L 0 10 z" class="arrow-head"></path>
                </marker>
                <marker id="arrowhead-cooperate" viewBox="0 0 10 10" refX="9" refY="5" markerWidth="8" markerHeight="8" orient="auto-start-reverse">
                    <path d="M 0 0 L 10 5 L 0 10 z" class="arrow-head"></path>
                </marker>
                <marker id="arrowhead-escape" viewBox="0 0 10 10" refX="9" refY="5" markerWidth="8" markerHeight="8" orient="auto-start-reverse">
                    <path d="M 0 0 L 10 5 L 0 10 z" class="arrow-head"></path>
                </marker>
                <marker id="arrowhead-inherit-energy" viewBox="0 0 10 10" refX="9" refY="5" markerWidth="8" markerHeight="8" orient="auto-start-reverse">
                    <path d="M 0 0 L 10 5 L 0 10 z" class="arrow-head"></path>
                </marker>
                <!-- Marker for start of bidirectional arrows -->
                <marker id="arrowhead-start-attack" viewBox="0 0 10 10" refX="1" refY="5" markerWidth="8" markerHeight="8" orient="auto">
                    <path d="M 0 5 L 10 0 L 10 10 z" class="arrow-head"></path>
                </marker>
                <marker id="arrowhead-start-cooperate" viewBox="0 0 10 10" refX="1" refY="5" markerWidth="8" markerHeight="8" orient="auto">
                    <path d="M 0 5 L 10 0 L 10 10 z" class="arrow-head"></path>
                </marker>
            </defs>
        </svg>

    </div>

    <div class="right-section">
        <!-- Environment Settings Panel -->
        <div class="environment-settings-panel">
            <div class="panel-title">Environment Settings</div>
            <div class="setting-item">
                <label for="grid-size">Grid Size:</label>
                <input type="number" id="grid-size" value="10" min="5" max="20">
            </div>
            <div class="setting-item">
                <label for="resource-spawn-chance">Resource Spawn Chance:</label>
                <input type="number" id="resource-spawn-chance" value="0.1" step="0.01" min="0" max="1">
            </div>
            <div class="setting-item">
                <label for="resource-max-amount">Max Resources:</label>
                <input type="number" id="resource-max-amount" value="5" min="1">
            </div>
            <div class="setting-item">
                <label for="child-energy-threshold">Child Energy Threshold:</label>
                <input type="number" id="child-energy-threshold" value="15" min="5">
            </div>
            <div class="setting-item">
                <label for="active-child-growth-rate">Active Child Growth Rate:</label>
                <input type="number" id="active-child-growth-rate" value="0.05" step="0.01" min="0.01" max="1">
            </div>
            <div class="setting-item">
                <label for="passive-child-growth-rate">Passive Child Growth Rate:</label>
                <input type="number" id="passive-child-growth-rate" value="0.2" step="0.01" min="0.01" max="1">
            </div>
            <div class="setting-item">
                <label for="child-proximity-for-creation">Child Proximity For Creation:</label>
                <input type="number" id="child-proximity-for-creation" value="2" min="1">
            </div>
            <div class="setting-item">
                <label for="power-gain-on-attack">Power Gain On Attack (Active):</label>
                <input type="number" id="power-gain-on-attack" value="5" min="0">
            </div>
            <div class="setting-item">
                <label for="resource-regeneration-rate">Resource Regeneration Rate:</label>
                <input type="number" id="resource-regeneration-rate" value="5" min="1">
            </div>
            <div class="setting-item">
                <label for="power-cost-of-child-creation">Power Cost of Child Creation:</label>
                <input type="number" id="power-cost-of-child-creation" value="3" min="0">
            </div>
            <div class="setting-item">
                <label for="passive-cooperation-proximity">Passive Coop Proximity:</label>
                <input type="number" id="passive-cooperation-proximity" value="2" min="1">
            </div>
            <div class="setting-item">
                <label for="passive-cooperation-attack-proximity">Passive Coop Attack Proximity:</label>
                <input type="number" id="passive-cooperation-attack-proximity" value="1.5" step="0.1" min="0.5">
            </div>
            <div class="setting-item">
                <label for="passive-cooperation-damage">Passive Coop Damage:</label>
                <input type="number" id="passive-cooperation-damage" value="3" min="0">
            </div>
            <div class="setting-item">
                <label for="child-generation-power-threshold">Child Gen Power Threshold:</label>
                <input type="number" id="child-generation-power-threshold" value="20" min="0">
            </div>
            <div class="setting-item">
                <label for="passive-danger-threshold">Passive Danger Threshold:</label>
                <input type="number" id="passive-danger-threshold" value="5" min="0">
            </div>
        </div>

        <div class="buttons-container">
            <button class="start-button" onclick="toggleSimulation()">Start Simulation</button>
            <button class="reset-button" onclick="resetSimulation()">Reset Simulation</button>
        </div>

        <!-- Entity Legend Panel -->
        <div class="entity-legend">
            <div class="panel-title">Entity Legend</div>
            <div class="legend-item"><span class="legend-color-box active-agent"></span> <span class="legend-icon">⚡</span> Active Agent</div>
            <div class="legend-item"><span class="legend-color-box passive-agent"></span> <span class="legend-icon">○○</span> Passive Agent</div>
            <div class="legend-item"><span class="legend-color-box resource-entity"></span> <span class="legend-icon">🌱</span> Resource Entity</div>
            <div class="legend-item"><span class="legend-color-box child-entity"></span> <span class="legend-icon">🧬</span> Child Entity</div>
            <div class="legend-item"><span class="legend-color-box obstacle-entity"></span> <span class="legend-icon">██</span> Obstacle Entity</div>
        </div>

        <!-- Simulation Log Panel -->
        <div class="simulation-log">
            <div class="panel-title">Simulation Log</div>
            <div id="log-content" class="log-content"></div>
        </div>
    </div>
    <script src="script.js"></script>
</body>
</html> 