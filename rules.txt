1.  **Life Cycle & Generation:**
    *   Entities exist within a life cycle: they are born/created, live, interact, and can be removed (e.g., through depletion of energy or successful attacks).
    *   Reproduction is a core mechanism for population generation, contingent on specific conditions being met by the parent entities.

2.  **Resource & Energy Management:**
    *   Entities require energy, primarily obtained by gathering resources, for survival and to perform actions.
    *   Resources are finite but replenish over time, simulating an ecosystem's cyclical nature.

3.  **Grid Occupancy & Movement:**
    *   Each grid square can be occupied by only one entity at any given time, ensuring distinct spatial presence.
    *   Entities navigate the grid, moving one square at a time in cardinal directions (vertical or horizontal), seeking goals (resources, other entities) or avoiding threats.
    *   Movement avoids occupied squares; if a direct path is blocked, entities will attempt to find an adjacent empty square.

4.  **Entity Roles & Interactions (Fairness & Justice):**
    *   **Active Agents:**
        *   Are the primary initiators of direct attacks against other entities, capable of individual offensive actions.
        *   Can gain the ability to generate children after demonstrating sufficient 'power' or 'improvement'.
        *   Engaging in attacks requires an energy expenditure, promoting strategic decision-making.
        *   Power is gained primarily through successful strategic interactions with other powerful entities.
    *   **Passive Agents:**
        *   Focus on survival, resource gathering, and avoidance of danger.
        *   Are not capable of initiating individual attacks.
        *   Can collectively defend against active agents when in cooperative proximity, acting as a deterrent.
        *   Possess a chance to resist or mitigate incoming attacks when individually targeted, ensuring a degree of self-preservation.
    *   **Children:**
        *   Are immature entities that grow and mature over time, inheriting characteristics from their parents.
        *   Their maturation process involves gradual improvement towards their inherited potential.
        *   **Children consume energy at a fixed rate (`childEnergyConsumptionRate`) as they grow, adding a survival challenge.**
    *   **Resources:**
        *   Are consumable elements essential for entity survival and growth.
        *   Regenerate and reappear on the grid, ensuring sustainability of the ecosystem.

5.  **Polymorphism, Inheritance, Abstraction, and Loose Coupling (Structural Goals):**
    *   The simulation's underlying code structure should embody these principles to ensure flexibility, maintainability, and extensibility.
    *   Entity types (Active, Passive, Child, Resource, Obstacle) demonstrate polymorphism in their behaviors and interactions, despite sharing a common base.
    *   Inheritance is reflected in how child entities acquire traits and potentials from their parents.
    *   Abstractions are used to define general entity properties and behaviors that can be implemented specifically by different entity types.
    *   Loose coupling is maintained between different simulation components (e.g., movement, interactions, rendering), allowing independent modifications without widespread impact.

6.  **Entity Hover Tooltips:**
    *   When hovering over any entity (Active Agent, Passive Agent, Resource, Child, Obstacle), a tooltip appears.
    *   The tooltip displays the entity's type and ID.
    *   For Active and Passive Agents: Shows Energy and Power.
    *   For Children: Shows Energy and Growth Progress.
    *   For Resources: Shows Current Amount and Growth Rate.
    *   Obstacles: Display "No stats".

7.  **Child Creation Logic:**
    *   A new child is created only if two Active Agents (or two Passive Agents) are nearby (within a configurable `childProximityForCreation`, default 2 cells).
    *   Both parent agents must have sufficient energy (equal to or exceeding `childEnergyThreshold`, default 10).
    *   Both parent agents must not be on a reproduction cooldown. They must wait for `reproductionCooldownTicks` (default 100) after creating a child before reproducing again.
    *   Upon child creation, `childEnergyThreshold` energy is deducted from *both* parent Active Agents.
    *   Upon child creation, `powerCostOfChildCreation` (default 2) power is deducted from *both* parent Active Agents.
    *   **Children inherit features (like power and speed) from their parents upon creation, and will improve to be like their parents' features as they grow and mature.**
    *   **When a child matures, its type (e.g., active or passive) will align with its parent's type (currently, an active child will mature into an active agent).**

8.  **Active Agent Attack and Power Gain:**
    *   Active Agents can attack other Active Agents, Passive Agents, and Children if in close proximity (distance < 1.5).
    *   Attack chance is higher if resources are scarce.
    *   On successful attack of a Passive Agent or Child, the attacked entity loses energy, and the attacking Active Agent gains `power` (default `powerGainOnAttack`, 5).

9.  **Resource System:**
    *   Resources regenerate a small `energyPerTurn` (default 1) each tick when visible.
    *   When a resource's `currentAmount` drops to 0 or below, it is marked as `isHidden` (not visible).
    *   **Hidden resources regenerate their `currentAmount` at a `resourceRegenerationRate` (default 5) per tick, and randomly reappear on the map when their `currentAmount` reaches 50.**
    *   New resources are spawned randomly only if visible resources are below `resourceMaxAmount` (default 5) AND there are no hidden resources to reactivate. This prioritizes reusing existing resource objects. The `resourceSpawnChance` (default 0.1) is low to ensure resource scarcity.

10. **Fairness and Object Minimization:**
    *   Depleted resources regenerate to allow re-entry into the simulation.
    *   Resource spawning prioritizes reactivating hidden resources to minimize object creation.
    *   **Passive agents can effectively escape from active agents when in danger.**

11. **Grid Occupancy Rule:**
    *   Only one entity can occupy a single grid square at any given time.
    *   During child creation, new children will only be placed in an empty grid square.
    *   During movement (for agents), if the target square is occupied, the agent will attempt to move to anT adjacent empty square. If no adjacent empty square is available, the agent will remain in its current position. 