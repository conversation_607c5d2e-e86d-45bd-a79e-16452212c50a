// This script will now orchestrate the simulation using functions from other files

const simulationContainer = document.querySelector('.simulation-container');
const logContent = document.getElementById('log-content'); // Get the log content div

// Function to add messages to the UI log panel
function logToUI(message) {
    if (logContent) {
        const p = document.createElement('p');
        p.textContent = `[Tick ${simulationState.tickCounter}] ${message}`;
        logContent.prepend(p); // Add to the top
        // Keep the log content from getting too long
        if (logContent.children.length > 50) {
            logContent.removeChild(logContent.lastChild);
        }
    }
}

// Simulation state object
const simulationState = {
    isRunning: false,
    tickCounter: 0,
    intervalId: null,
    entities: [], // This will be a deep copy of initial entities
    gridSize: 16, // 16x16 grid for complex behaviors
    cellSize: 40, // 40px per cell for 16x16 grid
    resourceSpawnChance: 0.1, // Reduced resource spawn chance
    resourceMaxAmount: 5, // Significantly reduced max resources
    passiveDangerThreshold: 5, // Danger level at which passive agents react
    childEnergyThreshold: 25, // Balanced child energy threshold
    activeChildGrowthRate: 0.08, // Balanced active child growth rate
    passiveChildGrowthRate: 0.12, // Balanced passive child growth rate
    childProximityForCreation: 2, // Max distance between two parents for child creation
    powerGainOnAttack: 4, // Balanced power gain on attack
    resourceRegenerationRate: 5, // Balanced resource regeneration
    powerCostOfChildCreation: 8, // Balanced power cost for child creation
    passiveCooperationProximity: 2, // Passive agents need to be within this distance to cooperate
    reproductionCooldownTicks: 100, // Agents must wait 100 ticks before reproducing again
    childEnergyConsumptionRate: 0.3, // Balanced child energy consumption per tick
    childGenerationPowerThreshold: 20, // Minimum power required for child generation
};

// Default simulation settings for reset functionality
const defaultSimulationSettings = {
    gridSize: 16,
    resourceSpawnChance: 0.1,
    resourceMaxAmount: 5,
    passiveDangerThreshold: 5,
    childEnergyThreshold: 25,
    activeChildGrowthRate: 0.08,
    passiveChildGrowthRate: 0.12,
    childProximityForCreation: 2,
    powerGainOnAttack: 5,
    resourceRegenerationRate: 5,
    powerCostOfChildCreation: 8,
    passiveCooperationProximity: 2,
    childGenerationPowerThreshold: 20,
    cellSize: 40, // Add cellSize to default settings if it's configurable in UI
};

// Function to apply settings from the UI to simulationState
function applySettingsFromUI() {
    simulationState.gridSize = parseInt(document.getElementById('grid-size').value);
    simulationState.resourceSpawnChance = parseFloat(document.getElementById('resource-spawn-chance').value);
    simulationState.resourceMaxAmount = parseInt(document.getElementById('resource-max-amount').value);
    simulationState.passiveDangerThreshold = parseInt(document.getElementById('passive-danger-threshold').value);
    simulationState.childEnergyThreshold = parseInt(document.getElementById('child-energy-threshold').value);
    simulationState.activeChildGrowthRate = parseFloat(document.getElementById('active-child-growth-rate').value);
    simulationState.passiveChildGrowthRate = parseFloat(document.getElementById('passive-child-growth-rate').value);
    simulationState.childProximityForCreation = parseFloat(document.getElementById('child-proximity-for-creation').value);
    simulationState.powerGainOnAttack = parseInt(document.getElementById('power-gain-on-attack').value);
    simulationState.resourceRegenerationRate = parseInt(document.getElementById('resource-regeneration-rate').value);
    simulationState.powerCostOfChildCreation = parseInt(document.getElementById('power-cost-of-child-creation').value);
    simulationState.passiveCooperationProximity = parseFloat(document.getElementById('passive-cooperation-proximity').value);
    simulationState.childGenerationPowerThreshold = parseInt(document.getElementById('child-generation-power-threshold').value);
    console.log("Applied settings from UI:", simulationState);
}

// Function to reset settings in the UI to default values
function resetSettingsToDefault() {
    document.getElementById('grid-size').value = defaultSimulationSettings.gridSize;
    document.getElementById('resource-spawn-chance').value = defaultSimulationSettings.resourceSpawnChance;
    document.getElementById('resource-max-amount').value = defaultSimulationSettings.resourceMaxAmount;
    document.getElementById('passive-danger-threshold').value = defaultSimulationSettings.passiveDangerThreshold;
    document.getElementById('child-energy-threshold').value = defaultSimulationSettings.childEnergyThreshold;
    document.getElementById('active-child-growth-rate').value = defaultSimulationSettings.activeChildGrowthRate;
    document.getElementById('passive-child-growth-rate').value = defaultSimulationSettings.passiveChildGrowthRate;
    document.getElementById('child-proximity-for-creation').value = defaultSimulationSettings.childProximityForCreation;
    document.getElementById('power-gain-on-attack').value = defaultSimulationSettings.powerGainOnAttack;
    document.getElementById('resource-regeneration-rate').value = defaultSimulationSettings.resourceRegenerationRate;
    document.getElementById('power-cost-of-child-creation').value = defaultSimulationSettings.powerCostOfChildCreation;
    document.getElementById('passive-cooperation-proximity').value = defaultSimulationSettings.passiveCooperationProximity;
    document.getElementById('child-generation-power-threshold').value = defaultSimulationSettings.childGenerationPowerThreshold;
    console.log("Reset settings to default values.");
}

// Territory Control System
function initializeTerritories() {
    simulationState.territories = new Map(); // territoryId -> {owner, center, radius, strength}
}

function claimTerritory(agent, position) {
    const territoryId = `${position.row},${position.col}`;
    const existingTerritory = simulationState.territories.get(territoryId);

    if (!existingTerritory || existingTerritory.strength < (agent.power || 0)) {
        simulationState.territories.set(territoryId, {
            owner: agent.id,
            center: position,
            radius: 2,
            strength: agent.power || 0,
            claimedTick: simulationState.tickCounter
        });
        logToUI(`${agent.label} claimed territory at (${position.row}, ${position.col})`);
        return true;
    }
    return false;
}

function isInTerritory(position, territoryCenter, radius) {
    const distance = Math.sqrt(
        Math.pow(position.row - territoryCenter.row, 2) +
        Math.pow(position.col - territoryCenter.col, 2)
    );
    return distance <= radius;
}

function getTerritoryOwner(position) {
    for (const [territoryId, territory] of simulationState.territories) {
        if (isInTerritory(position, territory.center, territory.radius)) {
            return territory.owner;
        }
    }
    return null;
}

// Initial entities (will be copied to simulationState.entities)
const initialEntities = [
    // Active Agents (⚔)
    { id: 'active1', type: 'active', label: 'Active 1', icon: 'A', position: { row: Math.floor(Math.random() * 16), col: Math.floor(Math.random() * 16) }, energy: 170, trait: 'High Energy', speed: 1, isDangerous: false, dangerLevel: 0, targetPosition: null, childGenerationThreshold: 10, power: 20, canReproduce: true, lastReproducedTick: 0 },
    { id: 'active2', type: 'active', label: 'Active 2', icon: 'A', position: { row: Math.floor(Math.random() * 16), col: Math.floor(Math.random() * 16) }, energy: 170, trait: 'Fast', speed: 2, isDangerous: false, dangerLevel: 0, targetPosition: null, childGenerationThreshold: 10, power: 18, canReproduce: true, lastReproducedTick: 0 },
    { id: 'active3', type: 'active', label: 'Active 3', icon: 'A', position: { row: Math.floor(Math.random() * 16), col: Math.floor(Math.random() * 16) }, energy: 170, trait: 'Aggressive', speed: 1.5, isDangerous: false, dangerLevel: 0, targetPosition: null, childGenerationThreshold: 10, power: 19, canReproduce: true, lastReproducedTick: 0 },
    { id: 'active4', type: 'active', label: 'Active 4', icon: 'A', position: { row: Math.floor(Math.random() * 16), col: Math.floor(Math.random() * 16) }, energy: 170, trait: 'Adaptive', speed: 1.2, isDangerous: false, dangerLevel: 0, targetPosition: null, childGenerationThreshold: 10, power: 17, canReproduce: true, lastReproducedTick: 0 },
    
    // Passive Agents (○○)
    { id: 'passive1', type: 'passive', label: 'Passive 1', icon: 'P', position: { row: Math.floor(Math.random() * 16), col: Math.floor(Math.random() * 16) }, energy: 250, trait: 'Shielded', speed: 0.5, isDangerous: false, dangerLevel: 0, targetPosition: null, canAttack: false, canReproduce: true, power: 5, lastReproducedTick: 0 },
    { id: 'passive2', type: 'passive', label: 'Passive 2', icon: 'P', position: { row: Math.floor(Math.random() * 16), col: Math.floor(Math.random() * 16) }, energy: 250, trait: 'Resilient', speed: 0.5, isDangerous: false, dangerLevel: 0, targetPosition: null, canAttack: false, canReproduce: true, power: 4, lastReproducedTick: 0 },

    // Resources (🌱)
    { id: 'resource1', type: 'resource', label: 'Resource 1', icon: '🌿', position: { row: Math.floor(Math.random() * 16), col: Math.floor(Math.random() * 16) }, currentAmount: 100, energyPerTurn: 1, growth: 0, isHidden: false },
    { id: 'resource2', type: 'resource', label: 'Resource 2', icon: '🌿', position: { row: Math.floor(Math.random() * 16), col: Math.floor(Math.random() * 16) }, currentAmount: 100, energyPerTurn: 1, growth: 0, isHidden: false },
];

function createEntityElement(entity) {
    const wrapper = document.createElement('div');
    wrapper.id = entity.id;
    wrapper.classList.add('entity-wrapper');
    wrapper.style.top = `${entity.position.row * simulationState.cellSize}px`;
    wrapper.style.left = `${entity.position.col * simulationState.cellSize}px`;

    const div = document.createElement('div');
    // Assign classes based on entity type for general styling and specific color
    div.classList.add('entity');
    if (entity.type === 'resource') {
        div.classList.add('resource');
    } else if (entity.type === 'obstacle') {
        div.classList.add('obstacle');
    } else if (entity.type === 'child') {
        div.classList.add('child-entity'); // Specific class for child styling
    } else if (entity.type === 'active') {
        div.classList.add('active-agent'); // Specific class for active agent styling
    } else if (entity.type === 'passive') {
        div.classList.add('passive-agent'); // Specific class for passive agent styling
    }

    // Determine the icon based on entity type and user request (explicitly matching legend)
    let entityIcon = entity.icon; // Default to existing icon from entity object
    if (entity.type === 'passive') {
        entityIcon = 'P'; // Match legend
    } else if (entity.type === 'active') {
        entityIcon = 'A'; // Match legend
    } else if (entity.type === 'child') {
        entityIcon = '🧬'; // Match legend
    } else if (entity.type === 'resource') {
        entityIcon = '🌱'; // Match legend
    } else if (entity.type === 'obstacle') {
        entityIcon = '██'; // Match legend
    }

    div.innerHTML = `
        ${entityIcon ? `<div class="icon">${entityIcon}</div>` : ''}
    `;
    wrapper.appendChild(div);
    simulationContainer.appendChild(wrapper);

    // Add event listeners for tooltips
    wrapper.addEventListener('mouseover', (e) => {
        console.log(`Mouse over ${entity.id}`);
        showEntityTooltip(entity, e);
    });
    wrapper.addEventListener('mouseout', () => {
        console.log(`Mouse out ${entity.id}`);
        hideEntityTooltip();
    });

    return wrapper;
}

let currentTooltip = null;

function showEntityTooltip(entity, event) {
    console.log(`Showing tooltip for ${entity.id}`);
    if (currentTooltip) {
        currentTooltip.remove();
        currentTooltip = null;
    }

    const tooltip = document.createElement('div');
    tooltip.classList.add('entity-tooltip');

    let tooltipContent = `<strong>Type:</strong> ${entity.type}<br><strong>ID:</strong> ${entity.id}<br>`;

    if (entity.type === 'active' || entity.type === 'passive') {
        tooltipContent += `<strong>Energy:</strong> ${entity.energy.toFixed(1)}<br><strong>Power:</strong> ${entity.power || 0}`; // Display energy and power
    } else if (entity.type === 'child') {
        // Determine child type based on parents for tooltip
        let childTypeName = 'Child';
        if (entity.parentTypes && entity.parentTypes.every(type => type === 'active')) {
            childTypeName = 'active child';
        } else if (entity.parentTypes && entity.parentTypes.every(type => type === 'passive')) {
            childTypeName = 'passive child';
        } // Could add mixed child type if needed

        tooltipContent = `<strong>Type:</strong> ${childTypeName}<br><strong>ID:</strong> ${entity.id}<br>`;
        tooltipContent += `<strong>Energy:</strong> ${entity.energy.toFixed(1)}<br><strong>Growth:</strong> ${entity.growthProgress.toFixed(1)}%`; // Display growth
        if (entity.parentIds && entity.parentIds.length > 0) {
            // Display only parent IDs, without duplicating types
            const parentInfo = entity.parentIds.join(', ');
            tooltipContent += `<br><strong>Parents:</strong> ${parentInfo}`; // Display parent IDs only
        }
    } else if (entity.type === 'resource') {
        tooltipContent = `<strong>Type:</strong> ${entity.type}<br><strong>ID:</strong> ${entity.id}<br>`;
        tooltipContent += `<strong>Amount:</strong> ${entity.currentAmount}<br><strong>Growth Rate:</strong> ${entity.energyPerTurn}`; // Display amount and growth rate
    } else if (entity.type === 'obstacle') {
        tooltipContent = `<strong>Type:</strong> ${entity.type}<br><strong>ID:</strong> ${entity.id}<br>`;
        tooltipContent += `No stats for obstacles`;
    }

    tooltip.innerHTML = tooltipContent;

    // Position the tooltip near the mouse cursor
    tooltip.style.left = `${event.pageX + 10}px`;
    tooltip.style.top = `${event.pageY + 10}px`;

    document.body.appendChild(tooltip);
    // Add the 'show' class to trigger the opacity transition
    requestAnimationFrame(() => {
        tooltip.classList.add('show');
    });
    currentTooltip = tooltip;
}

function hideEntityTooltip() {
    console.log('Hiding tooltip');
    if (currentTooltip) {
        // Trigger fade out before removing
        currentTooltip.classList.remove('show');
        currentTooltip.addEventListener('transitionend', function handler() {
            currentTooltip.removeEventListener('transitionend', handler);
            if (currentTooltip) {
                currentTooltip.remove();
                currentTooltip = null;
            }
        });
    }
}

function drawArrow(fromId, toId, typeClass, textLabel, isBidirectional = false) {
    const fromWrapper = document.getElementById(fromId);
    const toWrapper = document.getElementById(toId);

    if (!fromWrapper || !toWrapper) return;

    const fromRect = fromWrapper.getBoundingClientRect();
    const toRect = toWrapper.getBoundingClientRect();
    const containerRect = simulationContainer.getBoundingClientRect();

    const fromX = (fromRect.left + fromRect.right) / 2 - containerRect.left;
    const fromY = (fromRect.top + fromRect.bottom) / 2 - containerRect.top;
    const toX = (toRect.left + toRect.right) / 2 - containerRect.left;
    const toY = (toRect.top + toRect.bottom) / 2 - containerRect.top;

    const dx = toX - fromX;
    const dy = toY - fromY;
    const angle = Math.atan2(dy, dx);
    const radius = 20;

    const startX = fromX + radius * Math.cos(angle);
    const startY = fromY + radius * Math.sin(angle);
    const endX = toX - radius * Math.cos(angle);
    const endY = toY - radius * Math.sin(angle);

    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svg.classList.add('arrow', ...typeClass.split(' '));
    svg.style.position = 'absolute';
    svg.style.overflow = 'visible';

    const svgMinX = Math.min(startX, endX);
    const svgMinY = Math.min(startY, endY);
    const svgMaxX = Math.max(startX, endX);
    const svgMaxY = Math.max(startY, endY);

    const svgWidth = Math.max(1, svgMaxX - svgMinX);
    const svgHeight = Math.max(1, svgMaxY - svgMinY);

    svg.style.left = `${svgMinX}px`;
    svg.style.top = `${svgMinY}px`;
    svg.style.width = `${svgWidth}px`;
    svg.style.height = `${svgHeight}px`;

    const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
    line.setAttribute('x1', startX - svgMinX);
    line.setAttribute('y1', startY - svgMinY);
    line.setAttribute('x2', endX - svgMinX);
    line.setAttribute('y2', endY - svgMinY);
    line.classList.add('arrow-line');
    svg.appendChild(line);

    const markerId = `arrowhead-${typeClass.split(' ')[0]}`;
    line.setAttribute('marker-end', `url(#${markerId})`);

    if (isBidirectional) {
        const markerStartId = `arrowhead-start-${typeClass.split(' ')[0]}`;
        line.setAttribute('marker-start', `url(#${markerStartId})`);
    }

    if (textLabel && textLabel.trim() !== '') {
        const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        text.classList.add('arrow-text');
        text.textContent = textLabel;
        
        const textOffset = 10;
        const midX = (startX + endX) / 2;
        const midY = (startY + endY) / 2;

        const perpendicularAngle = angle + Math.PI / 2;
        const offsetX = textOffset * Math.cos(perpendicularAngle);
        const offsetY = textOffset * Math.sin(perpendicularAngle);

        const textX = midX + offsetX - svgMinX;
        const textY = midY + offsetY - svgMinY;

        text.setAttribute('x', textX);
        text.setAttribute('y', textY);

        const textAngle = angle * 180 / Math.PI;
        text.setAttribute('transform', `rotate(${textAngle}, ${textX}, ${textY})`);
        
        svg.appendChild(text);
    }

    simulationContainer.appendChild(svg);
}

// --- Enhanced Pathfinding (A* Algorithm) ---
function findPath(start, goal, gridSize) {
    const openSet = [];
    const closedSet = new Set();
    const cameFrom = new Map();
    const gScore = new Map();
    const fScore = new Map();

    const startKey = `${start.row},${start.col}`;
    const goalKey = `${goal.row},${goal.col}`;

    openSet.push(start);
    gScore.set(startKey, 0);
    fScore.set(startKey, heuristic(start, goal));

    while (openSet.length > 0) {
        // Find node with lowest fScore
        let current = openSet.reduce((lowest, node) => {
            const currentKey = `${node.row},${node.col}`;
            const lowestKey = `${lowest.row},${lowest.col}`;
            return fScore.get(currentKey) < fScore.get(lowestKey) ? node : lowest;
        });

        const currentKey = `${current.row},${current.col}`;

        if (currentKey === goalKey) {
            // Reconstruct path
            const path = [];
            while (current) {
                path.unshift(current);
                current = cameFrom.get(`${current.row},${current.col}`);
            }
            return path.length > 1 ? path[1] : null; // Return next step
        }

        openSet.splice(openSet.indexOf(current), 1);
        closedSet.add(currentKey);

        // Check neighbors
        const neighbors = [
            {row: current.row - 1, col: current.col}, // Up
            {row: current.row + 1, col: current.col}, // Down
            {row: current.row, col: current.col - 1}, // Left
            {row: current.row, col: current.col + 1}  // Right
        ];

        for (const neighbor of neighbors) {
            const neighborKey = `${neighbor.row},${neighbor.col}`;

            // Skip if out of bounds or occupied or already processed
            if (neighbor.row < 0 || neighbor.row >= gridSize ||
                neighbor.col < 0 || neighbor.col >= gridSize ||
                isPositionOccupied(neighbor.row, neighbor.col) ||
                closedSet.has(neighborKey)) {
                continue;
            }

            const tentativeGScore = gScore.get(currentKey) + 1;

            if (!openSet.some(n => n.row === neighbor.row && n.col === neighbor.col)) {
                openSet.push(neighbor);
            } else if (tentativeGScore >= gScore.get(neighborKey)) {
                continue;
            }

            cameFrom.set(neighborKey, current);
            gScore.set(neighborKey, tentativeGScore);
            fScore.set(neighborKey, tentativeGScore + heuristic(neighbor, goal));
        }
    }

    return null; // No path found
}

function heuristic(a, b) {
    return Math.abs(a.row - b.row) + Math.abs(a.col - b.col); // Manhattan distance
}

function isPositionOccupied(row, col) {
    return simulationState.entities.some(e =>
        e.position.row === row && e.position.col === col
    );
}

// --- Simulation Logic ---

function updateEntityStates() {
    simulationState.entities.filter(e => e.type === 'active' || e.type === 'passive' || e.type === 'child').forEach(entity => {
        if (entity.type === 'active') {
            entity.energy = Math.max(0, entity.energy - 1.0); // Balanced energy drain for active agents
        } else if (entity.type === 'passive') {
            entity.energy = Math.max(0, entity.energy - 0.9); // Balanced energy drain for passive agents
        } else if (entity.type === 'child') {
            entity.energy = Math.max(0, entity.energy - 0.3); // Moderate child energy consumption
        }
        
        // Passive agents: Danger perception based on proximity to active agents
        if (entity.type === 'passive') {
            let nearbyActiveAgents = simulationState.entities.filter(other => 
                other.type === 'active' && 
                Math.sqrt(Math.pow(entity.position.row - other.position.row, 2) + Math.pow(entity.position.col - other.position.col, 2)) < 3
            );
            entity.dangerLevel = nearbyActiveAgents.length; 
            entity.isDangerous = entity.dangerLevel > simulationState.passiveDangerThreshold;

            if (entity.isDangerous) {
                // console.log(`${entity.label} feels dangerous! Danger Level: ${entity.dangerLevel}`);
            }
        }

        // Child growth and maturation
        if (entity.type === 'child') {
            entity.growthProgress = Math.min(100, entity.growthProgress + (entity.assignedGrowthRate || simulationState.activeChildGrowthRate));
            
            // Gradual improvement towards inherited features during growth
            if (entity.growthProgress < 100 && entity.inheritedPower !== undefined && entity.inheritedSpeed !== undefined) {
                const growthFactor = (entity.assignedGrowthRate || simulationState.activeChildGrowthRate) / 100; // Use assigned growth rate
                entity.power = Math.min(entity.inheritedPower, entity.power + (entity.inheritedPower - entity.power) * growthFactor);
                entity.speed = Math.min(entity.inheritedSpeed, entity.speed + (entity.inheritedSpeed - entity.speed) * growthFactor);
            }

            if (entity.growthProgress >= 100 && !entity.isMature) {
                entity.isMature = true;

                // Determine mature type based on parent types
                let matureType;
                let matureIcon;

                if (entity.parentTypes && entity.parentTypes.every(type => type === 'active')) {
                    matureType = 'active';
                    matureIcon = 'A'; // Match legend for mature active
                } else if (entity.parentTypes && entity.parentTypes.every(type => type === 'passive')) {
                    matureType = 'passive';
                    matureIcon = 'P'; // Match legend for mature passive
                } else if (entity.parentTypes && entity.parentTypes.some(type => type === 'active') && entity.parentTypes.some(type => type === 'passive')) {
                    // Mixed parentage (e.g., one active, one passive) -> child becomes active
                    matureType = 'active';
                    matureIcon = 'A'; // Match legend for mature active
                } else {
                    // Fallback, though ideally parentTypes should always be defined
                    matureType = 'passive'; // Default to passive if parent types are unclear
                    matureIcon = 'P'; // Match legend for mature passive
                }

                entity.type = matureType; // Set mature type
                entity.label = `Mature ${entity.label.replace('Child ', '')}`; // Update label
                entity.icon = matureIcon; // Set icon based on mature type
                entity.energy = 50; // Starting energy for new agent
                
                // Fully adopt inherited speed and power upon maturation
                if (entity.inheritedSpeed !== undefined) entity.speed = entity.inheritedSpeed;
                if (entity.inheritedPower !== undefined) entity.power = entity.inheritedPower;

                entity.isDangerous = false;
                entity.dangerLevel = 0;
                entity.targetPosition = null;
                entity.canAttack = (matureType === 'active'); // Active agents can attack
                entity.canReproduce = (matureType === 'active'); // Active agents can reproduce

                // Inherit trait from parent, or assign a random one if not set
                if (!entity.trait) {
                    const traits = ['Shielded', 'Resilient', 'Observant', 'Calm', 'Fast', 'Strong', 'Adaptive', 'Aggressive', 'Stealthy', 'High Energy'];
                    entity.trait = traits[Math.floor(Math.random() * traits.length)];
                }
                logToUI(`${entity.label} has matured into a ${matureType} agent!`); // Log maturation

                // Trigger maturation animation
                const childElement = document.getElementById(entity.id);
                if (childElement) {
                    // Update the HTML element immediately to reflect new type for transition
                    updateEntityElement(entity, childElement);
                    // No explicit animation class needed here if CSS transitions handle it.
                    // The `transition` property on `.child-entity`, `.active-agent`, etc., handles this.
                }
            }
        }
    });

    // Remove dead agents (energy <= 0)
    const deadAgents = simulationState.entities.filter(e => (e.type === 'active' || e.type === 'passive') && e.energy <= 0);
    deadAgents.forEach(deadAgent => {
        logToUI(`${deadAgent.label} (${deadAgent.type}) died due to lack of energy.`);
        const deadAgentElement = document.getElementById(deadAgent.id);
        if (deadAgentElement) {
            deadAgentElement.classList.add('dying');
            // Remove element after animation completes
            deadAgentElement.addEventListener('animationend', () => {
                deadAgentElement.remove();
            });
        }
    });
    // Filter out entities with energy <= 0 after triggering animation for dead agents
    simulationState.entities = simulationState.entities.filter(e => !(e.type === 'active' || e.type === 'passive') || e.energy > 0);
}

function handleMovement() {
    simulationState.entities.filter(e => e.type === 'active' || e.type === 'passive').forEach(agent => {
        let intendedRow = agent.position.row;
        let intendedCol = agent.position.col;
        let moved = false;

        let targetEntity = null;
        let targetDirection = null; // To store {dr, dc} for the 1x1 move

        // Determine target based on priority
        // 1. Escape danger (for passive agents)
        if (agent.type === 'passive' && agent.isDangerous) {
            let closestActive = null;
            let minDistance = Infinity;
            simulationState.entities.filter(e => e.type === 'active').forEach(activeAgent => {
                const dist = Math.sqrt(Math.pow(agent.position.row - activeAgent.position.row, 2) + Math.pow(agent.position.col - activeAgent.position.col, 2));
                if (dist < minDistance) {
                    minDistance = dist;
                    closestActive = activeAgent;
                }
            });

            if (closestActive) {
                targetEntity = closestActive;
                const dy = agent.position.row - targetEntity.position.row;
                const dx = agent.position.col - targetEntity.position.col;

                // Prioritize moving away in one cardinal direction (1 step)
                if (Math.abs(dy) > Math.abs(dx)) { 
                    targetDirection = { dr: Math.sign(dy), dc: 0 };
                } else if (Math.abs(dx) > Math.abs(dy)) { 
                    targetDirection = { dr: 0, dc: Math.sign(dx) };
                } else if (dy !== 0 || dx !== 0) { // Equal distances, choose randomly
                    if (Math.random() < 0.5) {
                        targetDirection = { dr: Math.sign(dy), dc: 0 };
                    } else {
                        targetDirection = { dr: 0, dc: Math.sign(dx) };
                    }
                } else { // Already at target, stay put
                    targetDirection = { dr: 0, dc: 0 };
                }
            }
        }

        // 2. Move towards resource (enhanced detection for passive agents)
        if (!targetDirection && agent.energy < 70 && !agent.isDangerous) { // Agents seek resources if energy is below 70
            let closestResource = null;
            let minDistance = Infinity;
            // Enhanced detection range for passive agents (better monitoring ability)
            const detectionRange = agent.type === 'passive' ? 12 : 6; // Passive agents see twice as far

            simulationState.entities.filter(e => e.type === 'resource' && e.currentAmount > 0 && !e.isHidden).forEach(resource => {
                const dist = Math.sqrt(Math.pow(agent.position.row - resource.position.row, 2) + Math.pow(agent.position.col - resource.position.col, 2));
                // Only consider resources within detection range
                if (dist <= detectionRange && dist < minDistance) {
                    minDistance = dist;
                    closestResource = resource;
                }
            });

            if (closestResource) {
                targetEntity = closestResource;

                // Use enhanced A* pathfinding for better navigation
                const nextStep = findPath(agent.position, targetEntity.position, simulationState.gridSize);

                if (nextStep) {
                    const dy = nextStep.row - agent.position.row;
                    const dx = nextStep.col - agent.position.col;
                    targetDirection = { dr: dy, dc: dx };
                } else {
                    // Fallback to simple movement if pathfinding fails
                    const dy = targetEntity.position.row - agent.position.row;
                    const dx = targetEntity.position.col - agent.position.col;

                    if (Math.abs(dy) > Math.abs(dx)) {
                        targetDirection = { dr: Math.sign(dy), dc: 0 };
                    } else if (Math.abs(dx) > Math.abs(dy)) {
                        targetDirection = { dr: 0, dc: Math.sign(dx) };
                    } else if (dy !== 0 || dx !== 0) {
                        targetDirection = { dr: Math.sign(dy), dc: 0 };
                    } else {
                        targetDirection = { dr: 0, dc: 0 };
                    }
                }
            }
        }

        // 3. Move towards other agents (for interaction) or random if no clear target
        if (!targetDirection) {
            let closestAgent = null;
            let minDistance = Infinity;
            simulationState.entities.filter(e => (e.type === 'active' || e.type === 'passive') && e.id !== agent.id).forEach(otherAgent => {
                const dist = Math.sqrt(Math.pow(agent.position.row - otherAgent.position.row, 2) + Math.pow(agent.position.col - otherAgent.position.col, 2));
                if (dist < minDistance) {
                    minDistance = dist;
                    closestAgent = otherAgent;
                }
            });

            if (closestAgent) {
                targetEntity = closestAgent;
                const dy = targetEntity.position.row - agent.position.row;
                const dx = targetEntity.position.col - agent.position.col;

                // Prioritize moving towards target in one cardinal direction (1 step)
                if (Math.abs(dy) > Math.abs(dx)) { 
                    targetDirection = { dr: Math.sign(dy), dc: 0 };
                } else if (Math.abs(dx) > Math.abs(dy)) { 
                    targetDirection = { dr: 0, dc: Math.sign(dx) };
                } else if (dy !== 0 || dx !== 0) { // Equal distances, choose randomly
                    if (Math.random() < 0.5) {
                        targetDirection = { dr: Math.sign(dy), dc: 0 };
                    } else {
                        targetDirection = { dr: 0, dc: Math.sign(dx) };
                    }
                } else { // Already at target, stay put
                    targetDirection = { dr: 0, dc: 0 };
                }
            } else { // Truly random as a last resort if no other agents
                // Ensure random movement is strictly cardinal (1 square) or stay put
                const cardinalDirections = [
                    {dr: -1, dc: 0}, // Up
                    {dr: 1, dc: 0},  // Down
                    {dr: 0, dc: -1}, // Left
                    {dr: 0, dc: 1},  // Right
                    {dr: 0, dc: 0}   // Stay put
                ];
                targetDirection = cardinalDirections[Math.floor(Math.random() * cardinalDirections.length)];
            }
        }

        // Calculate intended position based on chosen targetDirection
        if (targetDirection) {
            intendedRow = agent.position.row + targetDirection.dr;
            intendedCol = agent.position.col + targetDirection.dc;
        }

        // Ensure intended movement is within grid boundaries
        intendedRow = Math.max(0, Math.min(simulationState.gridSize - 1, intendedRow));
        intendedCol = Math.max(0, Math.min(simulationState.gridSize - 1, intendedCol));

        // Check for collisions at the intended spot and try fallback if blocked
        // This is a comprehensive check for any entity at the target position
        const isTargetOccupied = (row, col, currentAgentId) => {
            return simulationState.entities.some(e => 
                e.id !== currentAgentId && 
                e.position.row === row && e.position.col === col
            );
        };

        if (!isTargetOccupied(intendedRow, intendedCol, agent.id)) {
            agent.position.row = intendedRow;
            agent.position.col = intendedCol;
            moved = true;
        } else { 
            // If blocked, try to find an empty adjacent cardinal spot
            const fallbackDirections = [
                {dr: 0, dc: 1}, {dr: 0, dc: -1}, {dr: 1, dc: 0}, {dr: -1, dc: 0} // Cardinal (no stay put here as agent already tried to move)
            ];
            // Randomize fallback order to prevent bias
            fallbackDirections.sort(() => Math.random() - 0.5); 

            for (const dir of fallbackDirections) {
                let tempRow = agent.position.row + dir.dr;
                let tempCol = agent.position.col + dir.dc;
                
                // Ensure fallback is within bounds
                tempRow = Math.max(0, Math.min(simulationState.gridSize - 1, tempRow));
                tempCol = Math.max(0, Math.min(simulationState.gridSize - 1, tempCol));

                if (!isTargetOccupied(tempRow, tempCol, agent.id)) {
                    agent.position.row = tempRow;
                    agent.position.col = tempCol;
                    moved = true;
                    break; // Found an empty spot, no need to check further
                }
            }
        }

        // Update visual position if moved
        if (moved) {
            const agentElement = document.getElementById(agent.id);
            if (agentElement) {
                // The position update is already handled by updateEntityElement in updateSimulation
                // Here we just ensure the element exists for potential immediate class additions for animations
            }
        }
    });
}

function handleInteractions() {
    // Clear existing arrows for dynamic drawing
    document.querySelectorAll('.arrow').forEach(arrow => arrow.remove());

    // Get current resource count for scarcity check
    const availableResources = simulationState.entities.filter(e => e.type === 'resource' && !e.isHidden).length; // Filter by isHidden
    const resourceScarcityThreshold = simulationState.resourceMaxAmount / 2; // Example: Scarce if less than half max resources
    const isResourceScarce = availableResources < resourceScarcityThreshold;

    simulationState.entities.forEach(entity1 => {
        simulationState.entities.forEach(entity2 => {
            if (entity1.id === entity2.id) return;

            const distance = Math.sqrt(
                Math.pow(entity1.position.row - entity2.position.row, 2) +
                Math.pow(entity1.position.col - entity2.position.col, 2)
            );

            // --- Gathering Interaction (Active/Passive -> Resource) ---
            if ((entity1.type === 'active' || entity1.type === 'passive') && entity2.type === 'resource' && distance < 2 && entity2.currentAmount > 0 && !entity2.isHidden) { // Check if resource is not hidden
                let gatherRate = 2.5; // Base gather rate
                if (entity1.trait === 'Fast') gatherRate = 3; // Trait bonus
                if (entity1.type === 'passive') gatherRate = 1.8; // Improved passive gather rate for balance
                if (entity1.type === 'passive' && entity1.isDangerous) gatherRate *= 0.7; // Less penalty when in danger
                
                const gatheredAmount = Math.min(gatherRate, entity2.currentAmount);
                entity1.energy += gatheredAmount;
                entity2.currentAmount -= gatheredAmount;
                // Balanced power gain from gathering resources
                if (entity1.type === 'passive') {
                    entity1.power = (entity1.power || 0) + 0.25; // Moderate power gain for passive gathering
                    logToUI(`${entity1.label} gained 0.25 power from gathering. Total power: ${entity1.power.toFixed(2)}`);
                } else if (entity1.type === 'active') {
                    entity1.power = (entity1.power || 0) + 0.3; // Moderate power gain for active gathering
                    logToUI(`${entity1.label} gained 0.3 power from gathering. Total power: ${entity1.power.toFixed(2)}`);
                }
                drawArrow(entity1.id, entity2.id, 'gather active-resource', '', false);
                logToUI(`${entity1.label} gathered ${gatheredAmount.toFixed(1)} from ${entity2.label}. Energy: ${entity1.energy.toFixed(1)}`); // Log gathering

                // Trigger feeding animation
                const agentElement = document.getElementById(entity1.id);
                if (agentElement) {
                    agentElement.querySelector('.entity').classList.add('feeding');
                    setTimeout(() => {
                        agentElement.querySelector('.entity').classList.remove('feeding');
                    }, 500); // Animation duration
                }
            }

            // --- Attack Interaction (Active -> Active/Passive/Child) ---
            // Passive agents should not attack, so only active agents initiate attacks
            if (entity1.type === 'active' && (entity2.type === 'active' || entity2.type === 'passive' || entity2.type === 'child') && distance < 1.5) {
                // Conditional combat: Active agents less likely to attack each other when resources are abundant
                let attackChance = isResourceScarce ? 0.4 : 0.1; // Base chance based on resource scarcity

                // Reduce attack chance for active-on-active combat when conditions favor cooperation
                if (entity2.type === 'active') {
                    // Less likely to attack when both have good energy (survival not threatened)
                    if (entity1.energy > 50 && entity2.energy > 50) {
                        attackChance *= 0.3; // 70% reduction in attack chance
                    }
                    // Less likely to attack when resources are abundant (no competition pressure)
                    if (!isResourceScarce) {
                        attackChance *= 0.2; // 80% reduction when resources abundant
                    }
                    // Less likely to attack potential mates (both have reproduction capability)
                    if ((entity1.power || 0) >= simulationState.childGenerationPowerThreshold &&
                        (entity2.power || 0) >= simulationState.childGenerationPowerThreshold) {
                        attackChance *= 0.4; // 60% reduction for potential mates
                    }
                }

                if (Math.random() < attackChance) {
                    let damage = 15; // Increased damage for active agents
                    let attackSuccessful = true;

                    // Passive agent's chance to dodge/resist
                    if (entity2.type === 'passive') {
                        if (Math.random() < 0.2) { // 20% chance to dodge/resist
                            damage *= 0.2; // Reduce damage to 20% (80% resisted)
                            entity1.energy = Math.max(0, entity1.energy - 1); // Attacker loses extra energy for resisted attack
                            attackSuccessful = false;
                            logToUI(`${entity2.label} resisted attack from ${entity1.label}!`); // Log resistance
                        }
                    }

                    entity2.energy = Math.max(0, entity2.energy - damage);

                    // Cost for attacking: Active agent loses energy
                    entity1.energy = Math.max(0, entity1.energy - 1); // Reduced energy cost per attack

                    // Active agent gains power from attacking (balanced for all target types)
                    const powerGained = entity2.type === 'active' ? simulationState.powerGainOnAttack : Math.floor(simulationState.powerGainOnAttack * 0.6);
                    entity1.power = (entity1.power || 0) + powerGained;
                    logToUI(`${entity1.label} gained ${powerGained} power from attacking ${entity2.label}. Total power: ${entity1.power}`); // Log power gain

                    // Active agents with high power can claim territory after successful attacks
                    if (entity1.type === 'active' && (entity1.power || 0) >= 30) {
                        claimTerritory(entity1, entity1.position);
                    }

                    drawArrow(entity1.id, entity2.id, 'attack', '', false);
                    logToUI(`${entity1.label} attacked ${entity2.label}. ${entity2.label} energy: ${entity2.energy.toFixed(1)}`); // Log attack

                    // Trigger attacked animation
                    const attackedElement = document.getElementById(entity2.id);
                    if (attackedElement) {
                        attackedElement.querySelector('.entity').classList.add('attacked');
                        setTimeout(() => {
                            attackedElement.querySelector('.entity').classList.remove('attacked');
                        }, 500); // Animation duration
                    }
                }
            }

            // --- Cooperate Interaction (Active <-> Passive, Passive <-> Passive) ---
            if (distance < 1.5) {
                // Active <-> Passive Cooperation
                if (entity1.type === 'active' && entity2.type === 'passive' && entity1.energy > 50 && entity2.energy < 50) {
                    if (Math.random() < 0.1) { // Active helps passive
                        entity1.energy -= 5; // Active gives energy
                        entity2.energy += 5; // Passive gains energy
                        drawArrow(entity1.id, entity2.id, 'cooperate', '', true); 
                        logToUI(`${entity1.label} cooperated with ${entity2.label} (Active helping Passive).`); // Log cooperation
                    }
                }
                // Passive <-> Passive Cooperation
                else if (entity1.type === 'passive' && entity2.type === 'passive' && entity1.energy > 10 && entity2.energy > 10) {
                    if (Math.random() < 0.1) { // 10% chance to cooperate
                        entity1.energy += 5;
                        entity2.energy += 5;
                        // Passive agents gain moderate power from cooperation
                        entity1.power = (entity1.power || 0) + 0.12; // Balanced power gain for cooperation
                        entity2.power = (entity2.power || 0) + 0.12; // Both gain power
                        logToUI(`${entity1.label} and ${entity2.label} gained 0.12 power from cooperation. Total power: ${entity1.power.toFixed(2)}`);
                        drawArrow(entity1.id, entity2.id, 'cooperate', '', true);
                        logToUI(`${entity1.label} cooperated with ${entity2.label}. Both gained energy.`); // Log cooperation
                    }
                }
            }

            // --- Passive agents do not attack ---
            // Passive agents focus only on cooperation, energy sharing, and reproduction
            // They do not initiate attacks against any entities

            // --- Inherit Interaction (Passive -> Child - support) ---
            if (entity1.type === 'passive' && entity2.type === 'child' && distance < 1.5) {
                if (Math.random() < 0.1) { // 10% chance to support growth
                    const growthBoost = 5; // Boost growth percentage
                    entity2.growthProgress = Math.min(100, entity2.growthProgress + growthBoost);
                    drawArrow(entity1.id, entity2.id, 'inherit', '', false); // Indicate support
                    logToUI(`${entity1.label} supported ${entity2.label}'s growth. Growth: ${entity2.growthProgress.toFixed(0)}%`); // Log child support
                }
            }
        });
    });

    // --- Child Generation by Active Agents ---
    simulationState.entities.filter(e => e.type === 'active' && e.energy >= simulationState.childEnergyThreshold && (e.power || 0) >= simulationState.childGenerationPowerThreshold).forEach(activeAgent1 => {
        // Check cooldown for activeAgent1
        if (simulationState.tickCounter - (activeAgent1.lastReproducedTick || 0) < simulationState.reproductionCooldownTicks) {
            return; // Agent is on cooldown
        }

        // Find a second active agent nearby with sufficient energy and power
        const activeAgent2 = simulationState.entities.find(e =>
            e.id !== activeAgent1.id &&
            e.type === 'active' &&
            e.energy >= simulationState.childEnergyThreshold &&
            (e.power || 0) >= simulationState.childGenerationPowerThreshold &&
            // Check cooldown for activeAgent2
            (simulationState.tickCounter - (e.lastReproducedTick || 0) >= simulationState.reproductionCooldownTicks) &&
            Math.sqrt(
                Math.pow(activeAgent1.position.row - e.position.row, 2) +
                Math.pow(activeAgent1.position.col - e.position.col, 2)
            ) < simulationState.childProximityForCreation
        );

        if (!activeAgent2) return; // No second parent found or second parent on cooldown

        // Find an empty spot for the new child near the parents
        let newChildPosition = null;
        const searchRadius = 2; // Search within 2 cells of activeAgent1
        for (let r = -searchRadius; r <= searchRadius; r++) {
            for (let c = -searchRadius; c <= searchRadius; c++) {
                let potentialRow = activeAgent1.position.row + r;
                let potentialCol = activeAgent1.position.col + c;

                if (potentialRow >= 0 && potentialRow < simulationState.gridSize && 
                    potentialCol >= 0 && potentialCol < simulationState.gridSize) {
                    const occupied = simulationState.entities.some(e => e.position.row === potentialRow && e.position.col === potentialCol);
                    if (!occupied) {
                        newChildPosition = { row: potentialRow, col: potentialCol };
                        break;
                    }
                }
            }
            if (newChildPosition) break;
        }

        if (newChildPosition) {
            const newChildId = `child${simulationState.tickCounter}-${Math.floor(Math.random() * 100)}`;
            const newChild = {
                id: newChildId,
                type: 'child',
                label: `Child ${activeAgent1.label.replace('Active ', '')}`,
                icon: '🧬', // Match legend
                position: newChildPosition,
                energy: 20, // Increased starting energy for a child
                trait: activeAgent1.trait, // Child inherits trait from first parent
                isMature: false,
                growthProgress: 0,
                parentTrait: activeAgent1.trait,
                power: Math.max(1, Math.floor(activeAgent1.power * 0.5)), // Inherit 50% of parent's power (min 1)
                speed: activeAgent1.speed > 0.5 ? (activeAgent1.speed * 0.5) : 0.5, // Inherit 50% of parent's speed (min 0.5)
                inheritedPower: activeAgent1.power, // Store parent's full power for future improvement
                inheritedSpeed: activeAgent1.speed, // Store parent's full speed for future improvement
                parentIds: [activeAgent1.id, activeAgent2.id], // Store IDs of both parents
                parentTypes: [activeAgent1.type, activeAgent2.type], // Store types of both parents
                // Assign growth rate based on target mature type (active in this case)
                assignedGrowthRate: simulationState.activeChildGrowthRate,
            };
            simulationState.entities.push(newChild);
            activeAgent1.energy -= simulationState.childEnergyThreshold; // Deduct energy from first parent
            activeAgent1.power = Math.max(0, activeAgent1.power - simulationState.powerCostOfChildCreation); // Deduct power from first parent
            activeAgent1.lastReproducedTick = simulationState.tickCounter; // Update last reproduced tick for activeAgent1

            activeAgent2.energy -= simulationState.childEnergyThreshold; // Deduct energy from second parent
            activeAgent2.power = Math.max(0, activeAgent2.power - simulationState.powerCostOfChildCreation); // Deduct power from second parent
            activeAgent2.lastReproducedTick = simulationState.tickCounter; // Update last reproduced tick for activeAgent2

            drawArrow(activeAgent1.id, newChild.id, 'inherit', '', false); // Draw inheritance arrow from parent1
            drawArrow(activeAgent2.id, newChild.id, 'inherit', '', false); // Draw inheritance arrow from parent2
            logToUI(`${activeAgent1.label} and ${activeAgent2.label} generated a new child: ${newChild.label}.`); // Log child creation
        }
    });

    // --- Child Generation by Passive Agents ---
    simulationState.entities.filter(e => e.type === 'passive' && e.energy >= simulationState.childEnergyThreshold && (e.power || 0) >= simulationState.childGenerationPowerThreshold).forEach(passiveAgent1 => {
        // Check cooldown for passiveAgent1
        if (simulationState.tickCounter - (passiveAgent1.lastReproducedTick || 0) < simulationState.reproductionCooldownTicks) {
            return; // Agent is on cooldown
        }

        // Find a second passive agent nearby with sufficient energy and power
        const passiveAgent2 = simulationState.entities.find(e =>
            e.id !== passiveAgent1.id &&
            e.type === 'passive' &&
            e.energy >= simulationState.childEnergyThreshold &&
            (e.power || 0) >= simulationState.childGenerationPowerThreshold &&
            // Check cooldown for passiveAgent2
            (simulationState.tickCounter - (e.lastReproducedTick || 0) >= simulationState.reproductionCooldownTicks) &&
            Math.sqrt(
                Math.pow(passiveAgent1.position.row - e.position.row, 2) +
                Math.pow(passiveAgent1.position.col - e.position.col, 2)
            ) < simulationState.childProximityForCreation
        );

        if (!passiveAgent2) return; // No second passive parent found or second parent on cooldown

        // Find an empty spot for the new child near the parents
        let newChildPosition = null;
        const searchRadius = 2; // Search within 2 cells of passiveAgent1
        for (let r = -searchRadius; r <= searchRadius; r++) {
            for (let c = -searchRadius; c <= searchRadius; c++) {
                let potentialRow = passiveAgent1.position.row + r;
                let potentialCol = passiveAgent1.position.col + c;

                if (potentialRow >= 0 && potentialCol >= 0 && 
                    potentialRow < simulationState.gridSize && potentialCol < simulationState.gridSize) {
                    const occupied = simulationState.entities.some(e => e.position.row === potentialRow && e.position.col === potentialCol);
                    if (!occupied) {
                        newChildPosition = { row: potentialRow, col: potentialCol };
                        break;
                    }
                }
            }
            if (newChildPosition) break;
        }

        if (newChildPosition) {
            const newChildId = `child${simulationState.tickCounter}-${Math.floor(Math.random() * 100)}`;
            const newChild = {
                id: newChildId,
                type: 'child',
                label: `Child ${passiveAgent1.label.replace('Passive ', '')}`,
                icon: '🧬', // Match legend
                position: newChildPosition,
                energy: 20, // Starting energy for a child
                trait: passiveAgent1.trait, // Child inherits trait from first parent
                isMature: false,
                growthProgress: 0,
                parentTrait: passiveAgent1.trait,
                power: Math.max(1, Math.floor(passiveAgent1.power * 0.5)), // Inherit 50% of parent's power (min 1)
                speed: passiveAgent1.speed > 0.5 ? (passiveAgent1.speed * 0.5) : 0.5, // Inherit 50% of parent's speed (min 0.5)
                inheritedPower: passiveAgent1.power, // Store parent's full power for future improvement
                inheritedSpeed: passiveAgent1.speed, // Store parent's full speed for future improvement
                parentIds: [passiveAgent1.id, passiveAgent2.id], // Store IDs of both parents
                parentTypes: [passiveAgent1.type, passiveAgent2.type], // Store types of both parents
                assignedGrowthRate: simulationState.passiveChildGrowthRate, // Passive children grow at their own rate
            };
            simulationState.entities.push(newChild);
            passiveAgent1.energy -= simulationState.childEnergyThreshold; // Deduct energy from first parent
            passiveAgent1.power = Math.max(0, passiveAgent1.power - simulationState.powerCostOfChildCreation); // Deduct power from first parent
            passiveAgent1.lastReproducedTick = simulationState.tickCounter; // Update last reproduced tick for passiveAgent1

            passiveAgent2.energy -= simulationState.childEnergyThreshold; // Deduct energy from second parent
            passiveAgent2.power = Math.max(0, passiveAgent2.power - simulationState.powerCostOfChildCreation); // Deduct power from second parent
            passiveAgent2.lastReproducedTick = simulationState.tickCounter; // Update last reproduced tick for passiveAgent2

            drawArrow(passiveAgent1.id, newChild.id, 'inherit', '', false); // Draw inheritance arrow from parent1
            drawArrow(passiveAgent2.id, newChild.id, 'inherit', '', false); // Draw inheritance arrow from parent2
            logToUI(`${passiveAgent1.label} and ${passiveAgent2.label} generated a new passive child: ${newChild.label}.`); // Log passive child creation
        }
    });
}

function updateResources() {
    simulationState.entities.filter(e => e.type === 'resource').forEach(resource => {
        if (resource.currentAmount <= 0) {
            // Resource is depleted, mark as hidden and start regeneration
            resource.isHidden = true;
            resource.currentAmount = Math.min(100, resource.currentAmount + simulationState.resourceRegenerationRate);
            if (resource.currentAmount >= 50) { // Arbitrary threshold to reappear
                // Find a new random empty position for the reappearing resource
                let newRow, newCol;
                let positionFound = false;
                for (let i = 0; i < 100; i++) { // Try up to 100 times to find an empty spot
                    newRow = Math.floor(Math.random() * simulationState.gridSize);
                    newCol = Math.floor(Math.random() * simulationState.gridSize);
                    const occupied = simulationState.entities.some(e => e.position.row === newRow && e.position.col === newCol);
                    if (!occupied) {
                        positionFound = true;
                        break;
                    }
                }

                if (positionFound) {
                    resource.position.row = newRow;
                    resource.position.col = newCol;
                    resource.isHidden = false;
                    logToUI(`${resource.label} has regenerated and reappeared at (${newRow}, ${newCol})!`); // Log resource reappearance
                } else {
                    // If no empty position found, keep it hidden for now and try again later
                    logToUI(`Could not find empty spot for ${resource.label}, remaining hidden.`); // Log failure to reappear
                }
            }
        } else if (resource.isHidden) {
            // Resource is hidden and regenerating
            resource.currentAmount = Math.min(100, resource.currentAmount + simulationState.resourceRegenerationRate);
            if (resource.currentAmount >= 50) { // Arbitrary threshold to reappear
                // Find a new random empty position for the reappearing resource
                let newRow, newCol;
                let positionFound = false;
                for (let i = 0; i < 100; i++) { // Try up to 100 times to find an empty spot
                    newRow = Math.floor(Math.random() * simulationState.gridSize);
                    newCol = Math.floor(Math.random() * simulationState.gridSize);
                    const occupied = simulationState.entities.some(e => e.position.row === newRow && e.position.col === newCol);
                    if (!occupied) {
                        positionFound = true;
                        break;
                    }
                }

                if (positionFound) {
                    resource.position.row = newRow;
                    resource.position.col = newCol;
                    resource.isHidden = false;
                    logToUI(`${resource.label} has regenerated and reappeared at (${newRow}, ${newCol})!`); // Log resource reappearance
                } else {
                    // If no empty position found, keep it hidden for now and try again later
                    logToUI(`Could not find empty spot for ${resource.label}, remaining hidden.`); // Log failure to reappear
                }
            }
        } else {
            // Resource is visible and active, continue normal regeneration
            resource.currentAmount = Math.min(100, resource.currentAmount + resource.energyPerTurn);
        }
    });

    // Enhanced resource spawning with clustering
    const visibleResources = simulationState.entities.filter(e => e.type === 'resource' && !e.isHidden).length;
    const hiddenResources = simulationState.entities.filter(e => e.type === 'resource' && e.isHidden).length;

    if (visibleResources < simulationState.resourceMaxAmount && hiddenResources === 0 && Math.random() < simulationState.resourceSpawnChance) {
        let newRow, newCol;
        let positionFound = false;

        // Try to spawn near existing resources (clustering)
        const existingResources = simulationState.entities.filter(e => e.type === 'resource' && !e.isHidden);
        if (existingResources.length > 0 && Math.random() < 0.7) { // 70% chance to cluster
            const nearResource = existingResources[Math.floor(Math.random() * existingResources.length)];
            const clusterRadius = 3;

            for (let i = 0; i < 50; i++) { // Try to find spot near existing resource
                const angle = Math.random() * 2 * Math.PI;
                const distance = Math.random() * clusterRadius + 1;
                newRow = Math.round(nearResource.position.row + Math.cos(angle) * distance);
                newCol = Math.round(nearResource.position.col + Math.sin(angle) * distance);

                if (newRow >= 0 && newRow < simulationState.gridSize &&
                    newCol >= 0 && newCol < simulationState.gridSize) {
                    const occupied = simulationState.entities.some(e => e.position.row === newRow && e.position.col === newCol);
                    if (!occupied) {
                        positionFound = true;
                        break;
                    }
                }
            }
        }

        // If clustering failed, try random placement
        if (!positionFound) {
            for (let i = 0; i < 100; i++) {
                newRow = Math.floor(Math.random() * simulationState.gridSize);
                newCol = Math.floor(Math.random() * simulationState.gridSize);
                const occupied = simulationState.entities.some(e => e.position.row === newRow && e.position.col === newCol);
                if (!occupied) {
                    positionFound = true;
                    break;
                }
            }
        }

        if (positionFound) {
            const newResourceId = `resource${simulationState.tickCounter}-${Math.floor(Math.random() * 100)}`;
            const newResource = {
                id: newResourceId,
                type: 'resource',
                label: 'New Resource',
                icon: '🌿', // Match legend
                position: { row: newRow, col: newCol },
                currentAmount: 100, // New resources start full
                energyPerTurn: 1,
                growth: 0,
                isHidden: false,
            };
            simulationState.entities.push(newResource);
            logToUI(`New resource spawned: ${newResource.label} at (${newResource.position.row}, ${newResource.position.col})`); // Log new resource
        }
    }
}

// Main simulation loop
function updateSimulation() {
    if (!simulationState.isRunning) return;

    simulationState.tickCounter++;
    logToUI(`--- Simulation Tick: ${simulationState.tickCounter} ---`); // Keep concise tick log

    // Update the tick counter display
    const tickCounterElement = document.getElementById('tick-counter');
    if (tickCounterElement) {
        tickCounterElement.textContent = simulationState.tickCounter;
    }

    updateEntityStates();
    handleMovement();
    handleInteractions();
    updateResources();

    // Optimized re-rendering: Update existing elements and add/remove as needed
    const currentEntityIds = new Set(simulationState.entities.map(e => e.id));
    
    // Remove elements for entities that no longer exist
    Array.from(simulationContainer.children).forEach(element => {
        if (!currentEntityIds.has(element.id) && !element.classList.contains('dying')) { // Don't remove dying elements immediately
            element.remove();
        }
    });

    // Update existing or create new elements
    simulationState.entities.forEach(entity => {
        let entityElement = document.getElementById(entity.id);
        if (!entityElement) {
            // Entity is new, create its element
            entityElement = createEntityElement(entity);
            // Do not append here, createEntityElement already appends
        } else {
            // Entity exists, update its element
            updateEntityElement(entity, entityElement);
        }
    });

    // Request next frame
    simulationState.intervalId = setTimeout(updateSimulation, 500); // Run every 500ms
}

// New function to update an existing entity's HTML element
function updateEntityElement(entity, element) {
    // Update position
    element.style.top = `${entity.position.row * simulationState.cellSize}px`;
    element.style.left = `${entity.position.col * simulationState.cellSize}px`;

    // Update classes based on type (for styling changes like maturation)
    const entityDiv = element.querySelector('.entity');
    if (entityDiv) {
        // Remove all specific type classes first to ensure correct application
        entityDiv.classList.remove('active-agent', 'passive-agent', 'child-entity', 'resource', 'obstacle');

        if (entity.type === 'resource') {
            entityDiv.classList.add('resource');
        } else if (entity.type === 'obstacle') {
            entityDiv.classList.add('obstacle');
        } else if (entity.type === 'child') {
            entityDiv.classList.add('child-entity');
        } else if (entity.type === 'active') {
            entityDiv.classList.add('active-agent');
        } else if (entity.type === 'passive') {
            entityDiv.classList.add('passive-agent');
        }

        // Update icon if it changes (e.g., child maturation)
        let currentIcon = entityDiv.querySelector('.icon');
        let newIconContent = '';
        if (entity.type === 'passive') {
            newIconContent = 'P';
        } else if (entity.type === 'active') {
            newIconContent = 'A';
        } else if (entity.type === 'child') {
            newIconContent = '🧬';
        } else if (entity.type === 'resource') {
            newIconContent = '🌱';
        } else if (entity.type === 'obstacle') {
            newIconContent = '██';
        }

        if (currentIcon && currentIcon.textContent !== newIconContent) {
            currentIcon.textContent = newIconContent;
        } else if (!currentIcon && newIconContent) { // Create icon if it doesn't exist and new content is available
             const iconDiv = document.createElement('div');
             iconDiv.classList.add('icon');
             iconDiv.textContent = newIconContent;
             entityDiv.appendChild(iconDiv);
        }
    }
    // Additional updates for energy, power, growth progress, etc., if needed for visual feedback
    // These typically would be handled by internal elements within .entity, if you add them.
}

function toggleSimulation() {
    const startButton = document.querySelector('.start-button');
    if (simulationState.isRunning) {
        console.log("Attempting to pause simulation...");
        logToUI("Simulation paused.");
        clearTimeout(simulationState.intervalId);
        simulationState.isRunning = false;
        startButton.textContent = 'Resume Simulation';
        console.log("Simulation paused.");
    } else {
        console.log("Attempting to start/resume simulation...");
        logToUI("Simulation started/resumed.");
        simulationState.isRunning = true;
        startButton.textContent = 'Pause Simulation';
        console.log("Simulation resumed.");
        // Apply settings from UI before initializing entities (if first start) or resuming
        applySettingsFromUI();
        if (simulationState.entities.length === 0) { // First start, initialize entities
            console.log("Initializing entities for the first time.");
            logToUI("Initializing entities for the first time.");
            simulationState.entities = JSON.parse(JSON.stringify(initialEntities)); // Deep copy
            initializeTerritories(); // Initialize territory system
            // Ensure initial random positions for active agents are set without overlap
            let currentPositions = new Set();
            simulationState.entities.forEach(entity => {
                if (entity.id.startsWith('active') || entity.id.startsWith('passive') || entity.type === 'resource') {
                    let newPos;
                    do {
                        newPos = { row: Math.floor(Math.random() * simulationState.gridSize), col: Math.floor(Math.random() * simulationState.gridSize) };
                    } while (currentPositions.has(`${newPos.row},${newPos.col}`));
                    entity.position = newPos;
                    currentPositions.add(`${newPos.row},${newPos.col}`);
                }
            });

            simulationContainer.innerHTML = '';
            simulationState.entities.forEach(entity => createEntityElement(entity));
            console.log("Initial entities rendered.");
            logToUI("Initial entities rendered.");
        }
        updateSimulation(); // Start or resume the loop
    }
}

function resetSimulation() {
    console.log("Resetting simulation...");
    logToUI("Simulation reset.");
    clearTimeout(simulationState.intervalId);
    simulationState.isRunning = false;
    simulationState.tickCounter = 0;

    // Reset the tick counter display
    const tickCounterElement = document.getElementById('tick-counter');
    if (tickCounterElement) {
        tickCounterElement.textContent = '0';
    }
    // Reset entities to initial state
    simulationState.entities = JSON.parse(JSON.stringify(initialEntities)); 
    // Ensure initial random positions for active agents are set without overlap on reset
    let currentPositions = new Set();
    simulationState.entities.forEach(entity => {
        if (entity.id.startsWith('active') || entity.id.startsWith('passive') || entity.type === 'resource') {
            let newPos;
            do {
                newPos = { row: Math.floor(Math.random() * simulationState.gridSize), col: Math.floor(Math.random() * simulationState.gridSize) };
            } while (currentPositions.has(`${newPos.row},${newPos.col}`));
            entity.position = newPos;
            currentPositions.add(`${newPos.row},${newPos.col}`);
        }
    });

    // Reset environment settings in UI
    resetSettingsToDefault();
    // Apply default settings to simulationState after resetting UI, in case simulation starts immediately after reset
    applySettingsFromUI();
    simulationContainer.innerHTML = ''; // Clear container
    simulationState.entities.forEach(entity => createEntityElement(entity)); // Re-render initial entities
    document.querySelector('.start-button').textContent = 'Start Simulation';
    console.log("Simulation reset.");
}

function pauseSimulation() {
    toggleSimulation();
}

// Expose to global scope for button click
window.toggleSimulation = toggleSimulation;
window.resetSimulation = resetSimulation;
window.pauseSimulation = pauseSimulation;

// Initial setup of environment settings panel toggle
document.addEventListener('DOMContentLoaded', () => {
    const environmentSettingsPanel = document.querySelector('.environment-settings-panel');
    const settingsPanelTitle = environmentSettingsPanel.querySelector('.panel-title');
    
    if (settingsPanelTitle) {
        settingsPanelTitle.addEventListener('click', () => {
            environmentSettingsPanel.classList.toggle('collapsed');
        });
    }
    // Initialize settings to default when the page loads
    resetSettingsToDefault();
});