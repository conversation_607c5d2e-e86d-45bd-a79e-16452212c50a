html, body {
    height: 100%; /* Ensure html and body take full viewport height */
    margin: 0;
    padding: 0;
    overflow: hidden; /* Prevent any scrolling */
}

body {
    overflow-y: hidden; /* Prevent body from scrolling, right-section will handle it */
    display: flex;
    flex-direction: row; /* Arrange left and right sections side-by-side */
    justify-content: space-between; /* Push sections to the edges */
    align-items: flex-start; /* Align sections to the top */
    min-height: 100vh;
    background-color: #1a1a1a; /* Dark background */
    font-family: Arial, sans-serif;
    color: white;
    position: relative; /* For absolute positioning of tick counter */
}

/* Tick Counter - Positioned independently at top center */
.tick-counter-container {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(51, 51, 51, 0.9);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: bold;
    color: #fff;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    z-index: 1000; /* Ensure it's above everything */
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

h1 {
    color: #eee;
    margin-bottom: 20px;
}

.simulation-container {
    position: relative;
    width: 576px; /* 16 cells * 36px - bigger for better visibility */
    height: 576px; /* 16 cells * 36px - perfect square */
    background-image: linear-gradient(to right, #333 1px, transparent 1px), linear-gradient(to bottom, #333 1px, transparent 1px);
    background-size: 36px 36px; /* 36px cells for excellent visibility */
    border: 2px solid #666;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    /* Grid is now centered by parent flex container */
}

/* Arrow Styling */
.arrow {
    position: absolute;
    pointer-events: none; /* Allows hover on elements below */
    z-index: 5; /* Ensure arrows are above grid lines */
}

.arrow-line {
    stroke-width: 3; /* Increased for better visibility */
    fill: none;
    transition: stroke-width 0.3s ease;
    stroke: white; /* Default stroke color for visibility */
}

.arrow-head {
    fill: currentColor;
}

.arrow-text {
    font-family: Arial, sans-serif;
    font-size: 9px; /* Slightly reduced for harmony */
    font-weight: bold;
    text-anchor: middle; /* Center text on path */
    pointer-events: auto; /* Allow hover on text */
    text-shadow: 0.5px 0.5px 1px rgba(0,0,0,0.5); /* Softer shadow */
    transition: opacity 0.2s ease-in-out;
}

/* Specific Arrow Colors and Styles */
.arrow.gather.active-resource .arrow-line,
.arrow.gather.active-resource .arrow-head,
.arrow.gather.active-resource .arrow-text {
    stroke: #ffd700; /* Yellow */
    fill: #ffd700;
    stroke-dasharray: 8 4; /* Dashed line for gathering */
}

.arrow.gather.passive-resource .arrow-line,
.arrow.gather.passive-resource .arrow-head,
.arrow.gather.passive-resource .arrow-text {
    stroke: #00ff00; /* Green as in image */
    fill: #00ff00;
    stroke-dasharray: 8 4; /* Dashed line for gathering */
}

.arrow.attack .arrow-line,
.arrow.attack .arrow-head,
.arrow.attack .arrow-text {
    stroke: #ff0000; /* Red */
    fill: #ff0000;
    stroke-dasharray: 3 3; /* Tightly dashed line for attacking */
}

.arrow.escape .arrow-line {
    stroke: #00ff00; /* Green */
    stroke-dasharray: 5 5; /* Dashed */
}
.arrow.escape .arrow-head {
    fill: #00ff00;
}
.arrow.escape .arrow-text {
    fill: #00ff00;
}

.arrow.cooperate .arrow-line,
.arrow.cooperate .arrow-head,
.arrow.cooperate .arrow-text {
    stroke: #0000ff; /* Blue */
    fill: #0000ff;
}

.arrow.inherit .arrow-line,
.arrow.inherit .arrow-head,
.arrow.inherit .arrow-text {
    stroke: #ffd700; /* Yellow */
    fill: #ffd700;
    stroke-dasharray: 5 5; /* Dashed */
}

.arrow.inherit-energy .arrow-line,
.arrow.inherit-energy .arrow-head,
.arrow.inherit-energy .arrow-text {
    stroke: #00ff00; /* Green */
    fill: #00ff00;
    stroke-dasharray: 10 2; /* Another dashed pattern for inherit-energy */
}

/* Arrow Legend Styling */
.connection-types-panel {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 10px 20px;
    background-color: #333;
    border-radius: 20px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4), 0 0 8px rgba(0, 0, 0, 0.2); /* Enhanced shadow */
    color: white;
    font-weight: bold;
    font-size: 14px;
    width: 250px;
    box-sizing: border-box;
}

.connection-types-panel span {
    /* Default styles for spans, if needed */
}

.attack-label {
    color: #ff0000; /* Red, matching Attack arrow */
}

.gather-label {
    color: #ffd700; /* Yellow, matching Gather arrow */
}

.inherit-label {
    color: #ffd700; /* Yellow, matching Inherit arrow */
}

.cooperate-label {
    color: #0000ff; /* Blue, matching Cooperate arrow */
}

/* Removing individual color styling as per new understanding */

/* Start Button Styling */
.start-button {
    padding: 10px 20px;
    font-size: 14px;
    font-weight: bold;
    color: white;
    background-color: #3cb371;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    box-shadow: 0 6px 12px rgba(0, 179, 113, 0.3), 0 0 6px rgba(0, 179, 113, 0.15); /* Enhanced shadow */
    transition: background-color 0.3s ease, transform 0.2s ease, box-shadow 0.3s ease;
}

.start-button:hover {
    background-color: #2e8b57;
    transform: translateY(-1px);
}

.start-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 5px rgba(0, 179, 113, 0.4);
}

.pause-button,
.reset-button {
    padding: 10px 20px;
    font-size: 14px;
    font-weight: bold;
    color: white;
    background-color: #333;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4), 0 0 6px rgba(0, 0, 0, 0.2); /* Enhanced shadow */
    transition: background-color 0.3s ease, transform 0.2s ease, box-shadow 0.3s ease;
}

.pause-button:hover,
.reset-button:hover {
    background-color: #555;
    transform: translateY(-1px);
}

.pause-button:active,
.reset-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
}

.entity-wrapper {
    position: absolute;
    width: 36px; /* Match 36px cell size */
    height: 36px; /* Perfect square cells */
    display: flex;
    justify-content: center;
    align-items: center;
    transition: transform 0.2s ease-out; /* Smooth transition for movement and hover effects */
}

.entity {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 32px; /* Larger size for 36px cells */
    height: 32px; /* Perfect square entities */
    font-size: 16px; /* Larger font for better visibility */
    background-color: #555;
    color: white;
    border: 2px solid #777; /* Proper border for visibility */
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
    transition: all 0.2s ease-in-out;
    position: relative; /* For positioning internal elements */
    border-radius: 50%; /* Make individual entities circular */
}

.entity:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.5);
    z-index: 10; /* Bring hovered entity to front */
}

.active-agent {
    background: linear-gradient(135deg, #ff6b35, #ff8c00); /* Orange gradient for active agents */
    border: 2px solid #ff8c00; /* Matching border */
    border-radius: 50%; /* Make circular */
    transform: none; /* Remove diamond shape rotation */
    transition: background-color 0.3s ease, border-color 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease; /* Added transitions */
    position: relative;
    color: white;
    font-weight: bold;
    font-size: 16px; /* Consistent with larger entity base size */
    display: flex;
    align-items: center;
    justify-content: center;
}

.active-agent::before {
    content: "⚔";
    font-size: 18px; /* Larger size for 36px cells */
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.active-agent .icon {
    transform: none; /* Remove counter-rotation */
}

.passive-agent {
    background: linear-gradient(135deg, #4682b4, #6a9ac9); /* SteelBlue gradient for passive agents */
    border: 2px solid #4682b4; /* Matching border */
    border-radius: 50%; /* Circle */
    box-shadow: 0 0 10px rgba(70, 130, 180, 0.5), inset 0 0 4px rgba(255, 255, 255, 0.4); /* Soft, diffused glow with inner highlight */
    transition: background-color 0.3s ease, border-color 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease; /* Ensuring transform is included */
    position: relative;
    color: white;
    font-weight: bold;
    font-size: 16px; /* Consistent with larger entity base size */
    display: flex;
    align-items: center;
    justify-content: center;
}

.passive-agent::before {
    content: "🛡";
    font-size: 16px; /* Larger size for 36px cells */
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.child-entity {
    background-color: #8a2be2; /* BlueViolet */
    border-color: #9370db; /* MediumPurple */
    border-radius: 50%; /* Circle */
    width: 60%; /* Smaller */
    height: 60%;
    font-size: 12px; /* Smaller icon for child entities */
    box-shadow: 0 0 8px rgba(138, 43, 226, 0.6), inset 0 0 3px rgba(255, 255, 255, 0.3); /* Glow with inner highlight */
    transition: background-color 0.5s ease, border-color 0.5s ease, transform 0.5s ease, box-shadow 0.5s ease, width 0.5s ease, height 0.5s ease, font-size 0.5s ease; /* Transitions for maturation */
}

.resource {
    background-color: #32cd32; /* LimeGreen */
    border-color: #008000; /* Green */
    border-radius: 8px; /* More rounded square */
    box-shadow: 0 0 12px rgba(50, 205, 50, 0.6), inset 0 0 4px rgba(255, 255, 255, 0.5); /* Glow with inner highlight */
    transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

.obstacle {
    background-color: #a9a9a9; /* DarkGray */
    border-color: #696969; /* DimGray */
    border-radius: 4px; /* Slightly rounded for subtle distinction */
    box-shadow: 0 0 5px rgba(169, 169, 169, 0.4), inset 0 0 2px rgba(255, 255, 255, 0.2); /* Subtle shadow with inner highlight */
    transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

.label {
    position: absolute;
    bottom: -15px; /* Adjust based on entity size */
    font-size: 10px;
    color: #eee;
    white-space: nowrap;
    pointer-events: none; /* Do not block interaction with entity */
    text-shadow: 1px 1px 2px rgba(0,0,0,0.7);
}

.icon {
    font-size: 24px; /* Default size for icons */
    line-height: 1; /* Ensure icon is vertically centered */
}

.energy-level, .trait-icon, .resource-amount, .growth-progress {
    position: absolute;
    font-size: 10px;
    font-weight: bold;
    color: white;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.7);
    pointer-events: none;
}

.main-layout-wrapper {
    display: flex;
    flex-direction: column; /* Stack grid and legend vertically */
    align-items: flex-start; /* Align grid and legend to the left */
    margin-top: 50px;
    margin-left: 50px;
}

.top-right-panel {
    display: flex;
    flex-direction: column; /* Stack connection types and buttons vertically */
    align-items: flex-end; /* Align contents to the right */
    gap: 15px;
    margin-top: 50px;
    margin-right: 50px;
}

.right-section {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 15px; /* Increased gap for better visual separation between panels/buttons */
    margin-top: 60px; /* Account for tick counter */
    margin-right: 20px;
    padding: 10px;
    padding-bottom: 20px;
    width: 320px; /* Fixed width to prevent interference with grid */
    overflow-y: auto;
    min-height: 0;
    height: calc(100vh - 80px); /* Account for tick counter and margins */
    scrollbar-width: none; /* For Firefox */
    -ms-overflow-style: none;  /* For Internet Explorer and Edge */
}

.right-section::-webkit-scrollbar {
    width: 0px;
    background: transparent; /* Make scrollbar invisible */
}

.right-section::-webkit-scrollbar-thumb {
    background: transparent; /* Make scrollbar handle invisible */
}

.buttons-container {
    display: flex;
    flex-direction: row; /* Arrange buttons horizontally */
    gap: 15px; /* Spacing between buttons */
    justify-content: space-between; /* Distribute buttons to fill space */
    width: 280px; /* Consistent width */
    box-sizing: border-box;
    padding: 15px;
    background-color: #333;
    border-radius: 12px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4), 0 0 8px rgba(0, 0, 0, 0.2);
}

.buttons-container .start-button,
.buttons-container .pause-button,
.buttons-container .reset-button {
    width: auto; /* Allow natural sizing before flex-grow */
    flex-grow: 1; /* Make all buttons grow to fill space equally */
    text-align: center;
}

.agent,
.resource,
.obstacle {
    width: 40px;
    height: 40px;
    border-radius: 50%; /* Make them circular */
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 16px; /* Consistent with 36px cells */
    transition: all 0.1s ease-out; /* Smooth transitions for movement and visual changes */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3); /* Softer, more modern shadow */
    border: 2px solid transparent; /* Default transparent border */
}

.agent:hover, .resource:hover, .obstacle:hover {
    transform: scale(1.1); /* Slight zoom on hover */
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4); /* Enhanced shadow on hover */
    border-color: #ffffff; /* White border on hover */
}

/* Specific styling for active, passive, child, resource, and obstacle entities */
.passive-agent {
    background-color: #4682b4; /* SteelBlue for passive agents */
    border: 2px solid #4682b4; /* Matching border */
}

.child-entity {
    background: linear-gradient(135deg, #ffd700, #ffed4e); /* Gold gradient for child entities */
    border: 2px solid #ffd700; /* Proper border for visibility */
    color: #8b4513; /* Brown color for contrast */
    width: 26px; /* Proportional size for children in 36px cells */
    height: 26px; /* Perfect square */
    box-shadow: 0 0 8px rgba(255, 215, 0, 0.6); /* Golden glow */
    position: relative;
    font-weight: bold;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.child-entity::before {
    content: "★";
    font-size: 14px; /* Larger size for 36px cells */
    color: #8b4513;
    text-shadow: 1px 1px 1px rgba(0,0,0,0.3);
}

.resource {
    background: linear-gradient(135deg, #3cb371, #66cdaa); /* Green gradient for resources */
    border: 2px solid #3cb371;
    position: relative;
    color: white;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
}

.resource::before {
    content: "🌿";
    font-size: 18px; /* Larger size for 36px cells */
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.obstacle {
    background: linear-gradient(135deg, #696969, #808080); /* Gray gradient for obstacles */
    border: 2px solid #696969;
    position: relative;
    color: white;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
}

.obstacle::before {
    content: "🗿";
    font-size: 16px; /* Larger size for 36px cells */
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.label {
    position: absolute;
    bottom: -18px; /* Position below the emoji */
    left: 50%;
    transform: translateX(-50%);
    font-size: 10px;
    color: #eee;
    white-space: nowrap; /* Prevent label from wrapping */
}

.icon {
    font-size: 16px; /* Consistent with 36px cells */
    line-height: 1;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3); /* Subtle shadow for better visibility */
}

.energy-level, .trait-icon, .resource-amount, .growth-progress {
    font-size: 5px; /* Very small for detail */
    margin: 0; /* Remove default margins */
    line-height: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
    width: 100%; /* Ensure it takes full width for ellipsis */
}

.danger-indicator {
    font-size: 10px; /* Larger for visibility */
    position: absolute;
    top: 2px;
    right: 2px;
    line-height: 1;
}

/* New Entity Legend Styling */
.entity-legend {
    background-color: #333;
    padding: 15px;
    border-radius: 12px;
    width: 280px;
    height: 180px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4), 0 0 8px rgba(0, 0, 0, 0.2); /* Enhanced shadow */
    flex-shrink: 0;
    box-sizing: border-box;
    margin-bottom: 15px;
    overflow-y: auto;
}

/* Hide scrollbar for legend panel */
.entity-legend::-webkit-scrollbar {
    display: none;
}

.entity-legend {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
}

.entity-legend .legend-item {
    display: flex;
    align-items: center;
    color: #ddd;
    font-size: 11px;
    font-weight: 500;
    white-space: nowrap;
    margin-bottom: 8px;
    padding: 4px 6px;
    border-radius: 4px;
    transition: background-color 0.2s ease, color 0.2s ease;
}

.entity-legend .legend-item:hover {
    background-color: rgba(255, 255, 255, 0.05);
    color: #fff;
}

.entity-legend .legend-color-box {
    width: 16px;
    height: 16px;
    border-radius: 50%; /* Make circular to match entities */
    margin-right: 8px;
    border: 1px solid rgba(255, 255, 255, 0.2); /* Subtle border */
    flex-shrink: 0;
}

/* Specific colors for legend boxes */
.entity-legend .legend-color-box.active-agent {
    background: linear-gradient(135deg, #ff6b35, #ff8c00); /* Orange gradient */
}
.entity-legend .legend-color-box.passive-agent {
    background: linear-gradient(135deg, #4682b4, #6a9ac9); /* Blue gradient */
}
.entity-legend .legend-color-box.resource-entity {
    background: linear-gradient(135deg, #3cb371, #66cdaa); /* Green gradient */
}
.entity-legend .legend-color-box.child-entity {
    background: linear-gradient(135deg, #ffd700, #ffed4e); /* Gold gradient */
}
.entity-legend .legend-color-box.obstacle-entity {
    background: linear-gradient(135deg, #696969, #808080); /* Gray gradient */
}

.entity-legend .legend-icon {
    font-size: 12px; /* Icon size in legend */
    margin-right: 6px;
    line-height: 1;
    flex-shrink: 0;
}

/* New wrappers for overall layout */
.left-section {
    display: flex;
    flex-direction: column;
    align-items: center; /* Center the grid horizontally */
    justify-content: center; /* Center the grid vertically */
    flex-grow: 1; /* Take available space */
    padding: 70px 20px 30px 20px; /* Adjusted padding for better fit */
    min-height: 0; /* Allow shrinking */
}



/* Entity Tooltip Styling */
.entity-tooltip {
    position: fixed;
    background-color: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 12px; /* Slightly more rounded for tooltips */
    font-size: 11px;
    pointer-events: none;
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5), 0 0 6px rgba(0, 0, 0, 0.3); /* Enhanced shadow */
    white-space: nowrap;
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
}

.entity-tooltip.show {
    opacity: 1;
}

.simulation-log {
    background-color: #2a2a2a;
    padding: 15px;
    border-radius: 12px;
    width: 280px;
    height: 180px;
    overflow-y: hidden;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4), 0 0 8px rgba(0, 0, 0, 0.2); /* Enhanced shadow */
    color: #ddd;
    font-family: 'Consolas', 'Courier New', monospace;
    font-size: 12px;
    flex-shrink: 0;
    box-sizing: border-box;
    border: 1px solid #444;
}

/* Remove scrollbar styling for simulation-log as it's no longer individually scrollable */
.simulation-log::-webkit-scrollbar,
.simulation-log::-webkit-scrollbar-track,
.simulation-log::-webkit-scrollbar-thumb,
.simulation-log::-webkit-scrollbar-thumb:hover {
    display: none; /* Hide scrollbar entirely */
}

.log-content {
    white-space: pre-wrap;
    word-wrap: break-word;
    padding: 5px;
    line-height: 1.4;
    height: calc(100% - 10px);
    overflow-y: auto;
}

/* Hide scrollbar for log content */
.log-content::-webkit-scrollbar {
    display: none;
}

.log-content {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
}

.log-content p {
    margin: 0 0 4px 0;
    padding: 4px 8px;
    border-radius: 4px;
    color: #ccc;
    font-size: 11px;
    transition: background-color 0.2s ease;
    border-left: 2px solid transparent;
}

.log-content p:hover {
    background-color: rgba(255, 255, 255, 0.08);
    border-left-color: #666;
}

.log-content p:last-child {
    margin-bottom: 0;
}

/* Enhanced log entry types */
.log-content p:contains("Tick") {
    color: #88c999;
    font-weight: 500;
    border-left-color: #88c999;
}

.log-content p:contains("Attack") {
    color: #ff9999;
    border-left-color: #ff9999;
}

.log-content p:contains("Feed") {
    color: #99ccff;
    border-left-color: #99ccff;
}

.panel-title {
    font-size: 15px;
    font-weight: 500;
    color: #ccc; /* Subtle color for titles */
    margin-bottom: 15px; /* Increased for better separation */
    text-align: center;
}

.legend-item, .connection-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    padding: 6px 8px;
    border-radius: 6px;
    transition: background-color 0.2s ease;
}

.legend-item:hover, .connection-item:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.environment-settings-panel {
    background-color: #333;
    padding: 15px;
    border-radius: 12px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4), 0 0 8px rgba(0, 0, 0, 0.2); /* Enhanced shadow */
    color: white;
    font-size: 13px;
    width: 280px; /* Consistent width */
    box-sizing: border-box;
    max-height: none; /* Allow natural height when not collapsed */
    transition: max-height 0.5s ease-out, padding 0.3s ease-out; /* Adjusted transition for smoother effect */
    margin-bottom: 15px;
}

.environment-settings-panel.collapsed {
    max-height: 50px; /* Height of the title bar plus padding */
    padding-bottom: 0; /* Remove padding when collapsed */
    border-radius: 25px; /* Increased border-radius for a pill-like shape */
}

.environment-settings-panel .panel-title {
    color: #ccc;
    margin-bottom: 15px;
    cursor: pointer; /* Indicate it's clickable */
    text-align: center; /* Center the title */
    font-size: 15px;
    font-weight: 500;
    position: relative;
    transition: color 0.2s ease;
}

.environment-settings-panel .panel-title:hover {
    color: #fff;
}

.environment-settings-panel .panel-title::after {
    content: '▼';
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 10px;
    color: #888;
    transition: transform 0.3s ease, color 0.2s ease;
}

.environment-settings-panel .panel-title:hover::after {
    color: #aaa;
}

.environment-settings-panel.collapsed .panel-title::after {
    transform: translateY(-50%) rotate(-90deg);
}

/* Setting Groups */
.setting-group {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #444;
}

.setting-group:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

/* Group Labels */
.group-label {
    font-size: 11px;
    font-weight: 500;
    color: #888;
    margin-bottom: 8px;
    padding: 4px 0;
    text-transform: uppercase;
    letter-spacing: 1px;
    border-bottom: 1px solid #555;
    opacity: 1;
    visibility: visible;
    transition: opacity 0.3s ease-out, visibility 0.3s ease-out;
    position: relative;
}

.environment-settings-panel.collapsed .panel-title {
    margin-bottom: 0; /* No margin when collapsed */
    text-align: center; /* Ensure title is centered when collapsed */
}

.environment-settings-panel .setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    padding: 4px 0;
    opacity: 1; /* Default to visible */
    visibility: visible; /* Default to visible */
    transition: opacity 0.3s ease-out, visibility 0.3s ease-out, margin-bottom 0.3s ease-out; /* Smooth transition for items */
}

.group-label + .setting-item {
    margin-top: 2px; /* Reduce space between group label and first setting */
}

.environment-settings-panel.collapsed .setting-item,
.environment-settings-panel.collapsed .setting-group,
.environment-settings-panel.collapsed .group-label {
    opacity: 0; /* Fade out items */
    visibility: hidden; /* Hide items */
    margin-bottom: 0; /* Collapse margin */
}

.environment-settings-panel label {
    flex: 1; /* Allow label to take available space */
    margin-right: 10px;
    font-weight: 500;
    color: #e0e0e0;
    line-height: 1.3;
    cursor: help; /* Indicate tooltip availability */
    /* Remove text truncation to show full labels */
}

.environment-settings-panel label:hover {
    color: #ffffff;
}

.environment-settings-panel input[type="number"] {
    width: 80px; /* Fixed width for consistency */
    padding: 6px 8px;
    border-radius: 6px;
    border: 1px solid #555;
    background-color: #444;
    color: white;
    text-align: center;
    font-size: 12px;
    transition: border-color 0.2s ease, background-color 0.2s ease;
}

.environment-settings-panel input[type="number"]:focus {
    outline: none;
    border-color: #777;
    background-color: #4a4a4a;
}

/* Animations for state changes */

/* Dying animation */
@keyframes fadeOutAndShrink {
    from { opacity: 1; transform: scale(1); }
    to { opacity: 0; transform: scale(0.5); }
}

.dying .entity {
    animation: fadeOutAndShrink 0.8s forwards;
}

/* Feeding animation */
@keyframes pulseGreen {
    0% { transform: scale(1); box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3); } /* Enhanced initial state */
    50% { transform: scale(1.1); box-shadow: 0 0 25px rgba(50, 205, 50, 1); } /* More noticeable pulse */
    100% { transform: scale(1); box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3); } /* Return to normal */
}

.feeding .entity {
    animation: pulseGreen 0.6s ease-in-out; /* Slightly longer duration for smoother pulse */
}

/* Attacked/Damaged animation */
@keyframes flashRedAndShake {
    0% { background-color: inherit; transform: translateX(0); } /* Start at normal */
    25% { background-color: #ff0000; transform: translateX(-8px); } /* Stronger shake left */
    50% { background-color: inherit; transform: translateX(8px); } /* Stronger shake right */
    75% { background-color: #ff0000; transform: translateX(-8px); } /* Stronger shake left again */
    100% { background-color: inherit; transform: translateX(0); } /* End at normal */
}

.attacked .entity {
    animation: flashRedAndShake 0.5s ease-in-out; /* Slightly longer duration for impact */
}

/* New animation for active agents to show they are 'alive' */
@keyframes pulseActive {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.02); opacity: 0.95; }
    100% { transform: scale(1); opacity: 1; }
}

.active-agent {
    animation: pulseActive 2s infinite ease-in-out; /* Subtle, continuous pulse */
}

/* Territory visual indicators */
.territory-marker {
    position: absolute;
    border: 2px dashed rgba(255, 140, 0, 0.6);
    background-color: rgba(255, 140, 0, 0.1);
    border-radius: 50%;
    pointer-events: none;
    z-index: 1;
    animation: territoryPulse 3s infinite ease-in-out;
}

@keyframes territoryPulse {
    0% { opacity: 0.3; transform: scale(0.95); }
    50% { opacity: 0.6; transform: scale(1.05); }
    100% { opacity: 0.3; transform: scale(0.95); }
}

/* Resource cluster indicators */
.resource-cluster {
    position: absolute;
    border: 1px solid rgba(60, 179, 113, 0.4);
    background-color: rgba(60, 179, 113, 0.05);
    border-radius: 8px;
    pointer-events: none;
    z-index: 0;
}