html, body {
    height: 100%; /* Ensure html and body take full viewport height */
    margin: 0;
    padding: 0;
}

body {
    overflow-y: hidden; /* Prevent body from scrolling, right-section will handle it */
    display: flex;
    flex-direction: row; /* Arrange left and right sections side-by-side */
    justify-content: space-between; /* Push sections to the edges */
    align-items: flex-start; /* Align sections to the top */
    min-height: 100vh;
    background-color: #1a1a1a; /* Dark background */
    font-family: Arial, sans-serif;
    color: white;
}

h1 {
    color: #eee;
    margin-bottom: 20px;
}

.simulation-container {
    position: relative;
    width: 500px; /* 10 cells * 50px */
    height: 500px; /* 10 cells * 50px */
    background-image: linear-gradient(to right, #333 1px, transparent 1px), linear-gradient(to bottom, #333 1px, transparent 1px);
    background-size: 50px 50px; /* Grid cell size */
    border: 1px solid #555;
    margin-bottom: 20px;
}

/* Arrow Styling */
.arrow {
    position: absolute;
    pointer-events: none; /* Allows hover on elements below */
    z-index: 5; /* Ensure arrows are above grid lines */
}

.arrow-line {
    stroke-width: 3; /* Increased for better visibility */
    fill: none;
    transition: stroke-width 0.3s ease;
    stroke: white; /* Default stroke color for visibility */
}

.arrow-head {
    fill: currentColor;
}

.arrow-text {
    font-family: Arial, sans-serif;
    font-size: 9px; /* Slightly reduced for harmony */
    font-weight: bold;
    text-anchor: middle; /* Center text on path */
    pointer-events: auto; /* Allow hover on text */
    text-shadow: 0.5px 0.5px 1px rgba(0,0,0,0.5); /* Softer shadow */
    transition: opacity 0.2s ease-in-out;
}

/* Specific Arrow Colors and Styles */
.arrow.gather.active-resource .arrow-line,
.arrow.gather.active-resource .arrow-head,
.arrow.gather.active-resource .arrow-text {
    stroke: #ffd700; /* Yellow */
    fill: #ffd700;
    stroke-dasharray: 8 4; /* Dashed line for gathering */
}

.arrow.gather.passive-resource .arrow-line,
.arrow.gather.passive-resource .arrow-head,
.arrow.gather.passive-resource .arrow-text {
    stroke: #00ff00; /* Green as in image */
    fill: #00ff00;
    stroke-dasharray: 8 4; /* Dashed line for gathering */
}

.arrow.attack .arrow-line,
.arrow.attack .arrow-head,
.arrow.attack .arrow-text {
    stroke: #ff0000; /* Red */
    fill: #ff0000;
    stroke-dasharray: 3 3; /* Tightly dashed line for attacking */
}

.arrow.escape .arrow-line {
    stroke: #00ff00; /* Green */
    stroke-dasharray: 5 5; /* Dashed */
}
.arrow.escape .arrow-head {
    fill: #00ff00;
}
.arrow.escape .arrow-text {
    fill: #00ff00;
}

.arrow.cooperate .arrow-line,
.arrow.cooperate .arrow-head,
.arrow.cooperate .arrow-text {
    stroke: #0000ff; /* Blue */
    fill: #0000ff;
}

.arrow.inherit .arrow-line,
.arrow.inherit .arrow-head,
.arrow.inherit .arrow-text {
    stroke: #ffd700; /* Yellow */
    fill: #ffd700;
    stroke-dasharray: 5 5; /* Dashed */
}

.arrow.inherit-energy .arrow-line,
.arrow.inherit-energy .arrow-head,
.arrow.inherit-energy .arrow-text {
    stroke: #00ff00; /* Green */
    fill: #00ff00;
    stroke-dasharray: 10 2; /* Another dashed pattern for inherit-energy */
}

/* Arrow Legend Styling */
.connection-types-panel {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 10px 20px;
    background-color: #333;
    border-radius: 20px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4), 0 0 8px rgba(0, 0, 0, 0.2); /* Enhanced shadow */
    color: white;
    font-weight: bold;
    font-size: 14px;
    width: 250px;
    box-sizing: border-box;
}

.connection-types-panel span {
    /* Default styles for spans, if needed */
}

.attack-label {
    color: #ff0000; /* Red, matching Attack arrow */
}

.gather-label {
    color: #ffd700; /* Yellow, matching Gather arrow */
}

.inherit-label {
    color: #ffd700; /* Yellow, matching Inherit arrow */
}

.cooperate-label {
    color: #0000ff; /* Blue, matching Cooperate arrow */
}

/* Removing individual color styling as per new understanding */

/* Start Button Styling */
.start-button {
    padding: 10px 20px;
    font-size: 14px;
    font-weight: bold;
    color: white;
    background-color: #3cb371;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    box-shadow: 0 6px 12px rgba(0, 179, 113, 0.3), 0 0 6px rgba(0, 179, 113, 0.15); /* Enhanced shadow */
    transition: background-color 0.3s ease, transform 0.2s ease, box-shadow 0.3s ease;
}

.start-button:hover {
    background-color: #2e8b57;
    transform: translateY(-1px);
}

.start-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 5px rgba(0, 179, 113, 0.4);
}

.pause-button,
.reset-button {
    padding: 10px 20px;
    font-size: 14px;
    font-weight: bold;
    color: white;
    background-color: #333;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4), 0 0 6px rgba(0, 0, 0, 0.2); /* Enhanced shadow */
    transition: background-color 0.3s ease, transform 0.2s ease, box-shadow 0.3s ease;
}

.pause-button:hover,
.reset-button:hover {
    background-color: #555;
    transform: translateY(-1px);
}

.pause-button:active,
.reset-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
}

.entity-wrapper {
    position: absolute;
    width: 50px; /* Each cell size */
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: transform 0.2s ease-out; /* Smooth transition for movement and hover effects */
}

.entity {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 80%; /* Adjust size for better fit */
    height: 80%;
    font-size: 20px; /* Adjust icon size */
    background-color: #555;
    color: white;
    border: 2px solid #777;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    transition: all 0.2s ease-in-out;
    position: relative; /* For positioning internal elements */
    border-radius: 50%; /* Make individual entities circular */
}

.entity:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.5);
    z-index: 10; /* Bring hovered entity to front */
}

.active-agent {
    background-color: #ff8c00; /* Orange background for active agents */
    border: 2px solid #ff8c00; /* Matching border */
    border-radius: 50%; /* Make circular */
    transform: none; /* Remove diamond shape rotation */
    transition: background-color 0.3s ease, border-color 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease; /* Added transitions */
}

.active-agent .icon {
    transform: none; /* Remove counter-rotation */
}

.passive-agent {
    background-color: #4682b4; /* SteelBlue for passive agents */
    border-color: #6a9ac9; /* Lighter SteelBlue */
    border-radius: 50%; /* Circle */
    box-shadow: 0 0 10px rgba(70, 130, 180, 0.5), inset 0 0 4px rgba(255, 255, 255, 0.4); /* Soft, diffused glow with inner highlight */
    transition: background-color 0.3s ease, border-color 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease; /* Ensuring transform is included */
}

.child-entity {
    background-color: #8a2be2; /* BlueViolet */
    border-color: #9370db; /* MediumPurple */
    border-radius: 50%; /* Circle */
    width: 60%; /* Smaller */
    height: 60%;
    font-size: 16px; /* Smaller icon */
    box-shadow: 0 0 8px rgba(138, 43, 226, 0.6), inset 0 0 3px rgba(255, 255, 255, 0.3); /* Glow with inner highlight */
    transition: background-color 0.5s ease, border-color 0.5s ease, transform 0.5s ease, box-shadow 0.5s ease, width 0.5s ease, height 0.5s ease, font-size 0.5s ease; /* Transitions for maturation */
}

.resource {
    background-color: #32cd32; /* LimeGreen */
    border-color: #008000; /* Green */
    border-radius: 8px; /* More rounded square */
    box-shadow: 0 0 12px rgba(50, 205, 50, 0.6), inset 0 0 4px rgba(255, 255, 255, 0.5); /* Glow with inner highlight */
    transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

.obstacle {
    background-color: #a9a9a9; /* DarkGray */
    border-color: #696969; /* DimGray */
    border-radius: 4px; /* Slightly rounded for subtle distinction */
    box-shadow: 0 0 5px rgba(169, 169, 169, 0.4), inset 0 0 2px rgba(255, 255, 255, 0.2); /* Subtle shadow with inner highlight */
    transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

.label {
    position: absolute;
    bottom: -15px; /* Adjust based on entity size */
    font-size: 10px;
    color: #eee;
    white-space: nowrap;
    pointer-events: none; /* Do not block interaction with entity */
    text-shadow: 1px 1px 2px rgba(0,0,0,0.7);
}

.icon {
    font-size: 24px; /* Default size for icons */
    line-height: 1; /* Ensure icon is vertically centered */
}

.energy-level, .trait-icon, .resource-amount, .growth-progress {
    position: absolute;
    font-size: 10px;
    font-weight: bold;
    color: white;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.7);
    pointer-events: none;
}

.main-layout-wrapper {
    display: flex;
    flex-direction: column; /* Stack grid and legend vertically */
    align-items: flex-start; /* Align grid and legend to the left */
    margin-top: 50px;
    margin-left: 50px;
}

.top-right-panel {
    display: flex;
    flex-direction: column; /* Stack connection types and buttons vertically */
    align-items: flex-end; /* Align contents to the right */
    gap: 15px;
    margin-top: 50px;
    margin-right: 50px;
}

.right-section {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 15px; /* Increased gap for better visual separation between panels/buttons */
    margin-top: 0;
    margin-right: 30px;
    padding: 10px;
    padding-bottom: 20px;
    flex-grow: 1;
    overflow-y: auto;
    min-height: 0;
    height: 100%;
    scrollbar-width: none; /* For Firefox */
    -ms-overflow-style: none;  /* For Internet Explorer and Edge */
}

.right-section::-webkit-scrollbar {
    width: 0px;
    background: transparent; /* Make scrollbar invisible */
}

.right-section::-webkit-scrollbar-thumb {
    background: transparent; /* Make scrollbar handle invisible */
}

.buttons-container {
    display: flex;
    flex-direction: row; /* Arrange buttons horizontally */
    gap: 15px; /* Spacing between buttons */
    justify-content: space-between; /* Distribute buttons to fill space */
    width: 250px; /* Consistent width */
    box-sizing: border-box;
}

.buttons-container .start-button,
.buttons-container .pause-button,
.buttons-container .reset-button {
    width: auto; /* Allow natural sizing before flex-grow */
    flex-grow: 1; /* Make all buttons grow to fill space equally */
    text-align: center;
}

.agent,
.resource,
.obstacle {
    width: 40px;
    height: 40px;
    border-radius: 50%; /* Make them circular */
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
    transition: all 0.1s ease-out; /* Smooth transitions for movement and visual changes */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3); /* Softer, more modern shadow */
    border: 2px solid transparent; /* Default transparent border */
}

.agent:hover, .resource:hover, .obstacle:hover {
    transform: scale(1.1); /* Slight zoom on hover */
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4); /* Enhanced shadow on hover */
    border-color: #ffffff; /* White border on hover */
}

/* Specific styling for active, passive, child, resource, and obstacle entities */
.passive-agent {
    background-color: #4682b4; /* SteelBlue for passive agents */
    border: 2px solid #4682b4; /* Matching border */
}

.child-entity {
    background-color: #ffffff; /* White background for child entities */
    border: 2px solid #ffffff; /* Matching border */
    color: #333; /* Darker emoji for contrast on white */
}

.resource {
    background-color: #3cb371; /* MediumSeaGreen */
    border: 2px solid #3cb371;
}

.obstacle {
    background-color: #696969; /* DimGray */
    border: 2px solid #696969;
}

.label {
    position: absolute;
    bottom: -18px; /* Position below the emoji */
    left: 50%;
    transform: translateX(-50%);
    font-size: 10px;
    color: #eee;
    white-space: nowrap; /* Prevent label from wrapping */
}

.icon {
    font-size: 28px; /* Larger icon size */
    line-height: 1;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3); /* Subtle shadow for better visibility */
}

.energy-level, .trait-icon, .resource-amount, .growth-progress {
    font-size: 5px; /* Very small for detail */
    margin: 0; /* Remove default margins */
    line-height: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
    width: 100%; /* Ensure it takes full width for ellipsis */
}

.danger-indicator {
    font-size: 10px; /* Larger for visibility */
    position: absolute;
    top: 2px;
    right: 2px;
    line-height: 1;
}

/* New Entity Legend Styling */
.entity-legend {
    background-color: #333;
    padding: 10px;
    border-radius: 20px;
    width: 250px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4), 0 0 8px rgba(0, 0, 0, 0.2); /* Enhanced shadow */
    flex-shrink: 0;
    box-sizing: border-box;
}

.entity-legend .legend-item {
    display: flex;
    align-items: center;
    color: white;
    font-size: 12px;
    font-weight: bold;
    white-space: nowrap;
}

.entity-legend .legend-color-box {
    width: 15px;
    height: 15px;
    border-radius: 3px; /* Slightly rounded corners for consistency */
    margin-right: 5px;
    border: 1px solid rgba(255, 255, 255, 0.3); /* Subtle border */
}

/* Specific colors for legend boxes */
.entity-legend .legend-color-box.active-agent {
    background-color: #ff8c00; /* Orange */
}
.entity-legend .legend-color-box.passive-agent {
    background-color: #4169e1; /* Blue */
}
.entity-legend .legend-color-box.resource-entity {
    background-color: #3cb371; /* Green */
}
.entity-legend .legend-color-box.child-entity {
    background-color: #dc143c; /* Red */
}
.entity-legend .legend-color-box.obstacle-entity {
    background-color: #333; /* Dark gray */
}

.entity-legend .legend-icon {
    font-size: 14px; /* Icon size in legend */
    margin-right: 5px;
    line-height: 1;
}

/* New wrappers for overall layout */
.left-section {
    display: flex;
    flex-direction: column; /* Stack simulation and legend vertically */
    align-items: flex-start; /* Align contents to the left */
    gap: 20px; /* Space between simulation and legend */
    margin-top: 30px; /* Adjusted to push down from top edge */
    margin-left: 30px; /* Adjusted to push from left edge */
}

/* Entity Tooltip Styling */
.entity-tooltip {
    position: fixed;
    background-color: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 12px; /* Slightly more rounded for tooltips */
    font-size: 11px;
    pointer-events: none;
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5), 0 0 6px rgba(0, 0, 0, 0.3); /* Enhanced shadow */
    white-space: nowrap;
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
}

.entity-tooltip.show {
    opacity: 1;
}

.simulation-log {
    background-color: #2a2a2a;
    padding: 10px;
    border-radius: 20px;
    width: 250px;
    height: 200px;
    overflow-y: hidden;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4), 0 0 8px rgba(0, 0, 0, 0.2); /* Enhanced shadow */
    color: #eee;
    font-family: 'Consolas', 'Courier New', monospace;
    font-size: 12px;
    flex-shrink: 0;
    box-sizing: border-box;
}

/* Remove scrollbar styling for simulation-log as it's no longer individually scrollable */
.simulation-log::-webkit-scrollbar,
.simulation-log::-webkit-scrollbar-track,
.simulation-log::-webkit-scrollbar-thumb,
.simulation-log::-webkit-scrollbar-thumb:hover {
    display: none; /* Hide scrollbar entirely */
}

.log-content {
    white-space: pre-wrap;
    word-wrap: break-word;
    padding: 5px;
    line-height: 1.4;
}

.log-content p {
    margin: 0; /* Remove default paragraph margins for tighter lines */
    padding: 0; /* Ensure no extra padding */
}

.panel-title {
    font-size: 1.1em;
    font-weight: bold;
    color: #00bcd4; /* Accent color for titles */
    margin-bottom: 15px; /* Increased for better separation */
    text-align: center;
}

.legend-item, .connection-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.environment-settings-panel {
    background-color: #333;
    padding: 10px;
    border-radius: 8px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4), 0 0 8px rgba(0, 0, 0, 0.2); /* Enhanced shadow */
    color: white;
    font-size: 14px;
    width: 250px;
    box-sizing: border-box;
    max-height: none; /* Allow natural height when not collapsed */
    transition: max-height 0.5s ease-out, padding 0.3s ease-out; /* Adjusted transition for smoother effect */
}

.environment-settings-panel.collapsed {
    max-height: 40px; /* Height of the title bar plus padding */
    padding-bottom: 0; /* Remove padding when collapsed */
    border-radius: 20px; /* Increased border-radius for a pill-like shape */
}

.environment-settings-panel .panel-title {
    color: #00bcd4;
    margin-bottom: 10px;
    cursor: pointer; /* Indicate it's clickable */
    text-align: center; /* Center the title */
}

.environment-settings-panel.collapsed .panel-title {
    margin-bottom: 0; /* No margin when collapsed */
    text-align: center; /* Ensure title is centered when collapsed */
}

.environment-settings-panel .setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
    opacity: 1; /* Default to visible */
    visibility: visible; /* Default to visible */
    transition: opacity 0.3s ease-out, visibility 0.3s ease-out, margin-bottom 0.3s ease-out; /* Smooth transition for items */
}

.environment-settings-panel.collapsed .setting-item {
    opacity: 0; /* Fade out items */
    visibility: hidden; /* Hide items */
    margin-bottom: 0; /* Collapse margin */
}

.environment-settings-panel label {
    flex-basis: 60%; /* Adjust label width */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.environment-settings-panel input[type="number"] {
    flex-basis: 35%; /* Adjust input width */
    padding: 3px;
    border-radius: 8px; /* More rounded to match panels */
    border: 1px solid #555;
    background-color: #444;
    color: white;
    text-align: center;
}

/* Animations for state changes */

/* Dying animation */
@keyframes fadeOutAndShrink {
    from { opacity: 1; transform: scale(1); }
    to { opacity: 0; transform: scale(0.5); }
}

.dying .entity {
    animation: fadeOutAndShrink 0.8s forwards;
}

/* Feeding animation */
@keyframes pulseGreen {
    0% { transform: scale(1); box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3); } /* Enhanced initial state */
    50% { transform: scale(1.1); box-shadow: 0 0 25px rgba(50, 205, 50, 1); } /* More noticeable pulse */
    100% { transform: scale(1); box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3); } /* Return to normal */
}

.feeding .entity {
    animation: pulseGreen 0.6s ease-in-out; /* Slightly longer duration for smoother pulse */
}

/* Attacked/Damaged animation */
@keyframes flashRedAndShake {
    0% { background-color: inherit; transform: translateX(0); } /* Start at normal */
    25% { background-color: #ff0000; transform: translateX(-8px); } /* Stronger shake left */
    50% { background-color: inherit; transform: translateX(8px); } /* Stronger shake right */
    75% { background-color: #ff0000; transform: translateX(-8px); } /* Stronger shake left again */
    100% { background-color: inherit; transform: translateX(0); } /* End at normal */
}

.attacked .entity {
    animation: flashRedAndShake 0.5s ease-in-out; /* Slightly longer duration for impact */
}

/* New animation for active agents to show they are 'alive' */
@keyframes pulseActive {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.02); opacity: 0.95; }
    100% { transform: scale(1); opacity: 1; }
}

.active-agent {
    animation: pulseActive 2s infinite ease-in-out; /* Subtle, continuous pulse */
} 